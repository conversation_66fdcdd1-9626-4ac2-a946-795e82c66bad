<?php

namespace App\Http\Controllers\MobileApp\v1\Session;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SessionsAndSemesters\SessionConfig;
use App\Models\SessionsAndSemesters\SemesterConfig;
use App\Models\Courses\CourseRegistration;
use App\Models\SessionsAndSemesters\Session;
use App\Http\Resources\SessionConfigResource;
use App\Models\SessionsAndSemesters\Semester;
use App\Http\Resources\MobileApp\Institution\SessionResourceCollection;


class SessionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except('getCurrentSession', 'index');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query_builder = Session::buildQueryFromRequestParams($request->filters ?? [], $request->search_string, $request->id, $request->id_name)
            ->when($request->include_semesters, function ($query) {
                $query->with('semesters');
            });
        $no_of_batches = ceil(($query_builder->count()) / 4000);
        $sessions = new SessionResourceCollection($query_builder->paginate($request->items_per_page));

        return response()->json([
            'sessions' => $sessions,
            'no_of_batches' => $no_of_batches,
            'pagination' => [
                'total'        => $sessions->total(),
                'per_page'     => $sessions->perPage(),
                'current_page' => $sessions->currentPage(),
                'last_page'    => $sessions->lastPage(),
                'from'         => $sessions->firstItem(),
                'to'           => $sessions->lastItem(),
            ]
        ], 200);
    }

    //----------------------------------------------------------------------------------------------------------
    public function getCurrentSession($institute_id = null)
    {
        $institute_id = $institute_id == 'null' ? null : $institute_id;
        $active_session_status = \config('enums.session_status')['Active'];

        return Session::with('semesters.semester_setting')
            ->where('status', $active_session_status)
            ->where('institute_id', $institute_id)
            ->first();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
