import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/network/providers/internet_checker.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_response.dart';
import '../models/course_registration_model.dart';
import '../sources/remote_courses_source.dart';
import '../sources/local_courses_source.dart';
import '../sources/local_course_specifications_source.dart';
import '../sources/local_credit_load_limits_source.dart';

part 'courses_repository.g.dart';

@riverpod
CoursesRepository coursesRepository(Ref ref) {
  final remote = ref.read(remoteCoursesSourceProvider);
  final localRegistrations = ref.read(localCourseRegistrationsSourceProvider);
  final localSpecs = ref.read(localCourseSpecificationsSourceProvider);
  final localCreditLimits = ref.read(localCreditLoadLimitsSourceProvider);
  final internetChecker = ref.read(internetCheckerProvider);
  return CoursesRepository(remote, localRegistrations, localSpecs,
      localCreditLimits, internetChecker);
}

class CoursesRepository {
  final RemoteCoursesSource remote;
  final LocalCourseRegistrationsSource localRegistrations;
  final LocalCourseSpecificationsSource localSpecs;
  final LocalCreditLoadLimitsSource localCreditLimits;
  final InternetConnectionChecker internetChecker;

  CoursesRepository(
    this.remote,
    this.localRegistrations,
    this.localSpecs,
    this.localCreditLimits,
    this.internetChecker,
  );

  Future<List<CourseRegistration>> fetchCourseRegistrations(
      int? sessionId) async {
    final hasConnection = await internetChecker.hasConnection;

    if (hasConnection) {
      try {
        final regs = await remote.fetchCourseRegistrations(sessionId);
        await localRegistrations.saveCourseRegistrations(regs);
        return regs;
      } catch (_) {
        return await localRegistrations.getCourseRegistrations(sessionId);
      }
    } else {
      return await localRegistrations.getCourseRegistrations(sessionId);
    }
  }

  Future<CourseSpecificationResponse> fetchCourseSpecifications(
      int? sessionId) async {
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final response = await remote.fetchCourseSpecifications(sessionId);
        // Save both course specifications and credit limits to local cache
        await localSpecs
            .saveCourseSpecifications(response.courseSpecifications);
        await localCreditLimits.saveCreditLoadLimits(response.creditLoadLimits);
        return response;
      } catch (_) {
        // Fallback to cached data with cached credit limits
        final cachedSpecs = await localSpecs.getCourseSpecifications(sessionId);
        final cachedLimits = await localCreditLimits.getCreditLoadLimits();
        return CourseSpecificationResponse(
          sessionId: sessionId ?? 0,
          sessionName: '',
          courseSpecifications: cachedSpecs,
          creditLoadLimits: cachedLimits,
        );
      }
    } else {
      // Offline mode - return cached data with cached credit limits
      final cachedSpecs = await localSpecs.getCourseSpecifications(sessionId);
      final cachedLimits = await localCreditLimits.getCreditLoadLimits();
      return CourseSpecificationResponse(
        sessionId: sessionId ?? 0,
        sessionName: '',
        courseSpecifications: cachedSpecs,
        creditLoadLimits: cachedLimits,
      );
    }
  }

  Future<void> submitCourseRegistrations(
      List<Map<String, dynamic>> registrations) async {
    final hasConnection = await internetChecker.hasConnection;
    if (!hasConnection) {
      throw Exception('No internet connection');
    }

    await remote.submitCourseRegistrations(registrations);

    // Clear local course registrations cache after successful submission
    // This ensures fresh data is fetched when course form screen loads
    await _clearLocalCourseRegistrations();
  }

  Future<void> _clearLocalCourseRegistrations() async {
    try {
      // Clear course registrations from local database
      await localRegistrations.clearCourseRegistrations();
    } catch (e) {
      logger.e('Error clearing local course registrations: $e');
      // Don't rethrow - this is not critical for the submission success
    }
  }
}
