import 'package:drift/drift.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/local_database.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_model.dart';
import 'package:tertiary_mobile/shared/data/models/department.dart';

part 'local_courses_source.g.dart';

@riverpod
LocalCourseRegistrationsSource localCourseRegistrationsSource(Ref ref) {
  final db = ref.read(localDatabaseProvider);

  return LocalCourseRegistrationsSource(db);
}

class LocalCourseRegistrationsSource {
  final LocalDatabase _db;

  LocalCourseRegistrationsSource(this._db);

  Future<void> saveCourseRegistrations(List<CourseRegistration> regs) async {
    try {
      await _db.batch((batch) {
        // Use upsert for courses
        final courses = regs.map((e) => e.course).toSet().toList();
        batch.insertAll(
          _db.coursesTable,
          courses.map(
            (course) => CoursesTableCompanion.insert(
              courseCode: course.courseCode,
              courseTitle: course.courseTitle,
              creditUnit: course.creditUnit,
              type: course.type ?? 'N/A',
              hostId: Value(course.host?.id ?? 0),
              departmentId: Value(course.department?.id ?? 0),
              courseSynopsis: course.courseSynopsis != null
                  ? Value(course.courseSynopsis!)
                  : const Value.absent(),
              passMark: course.passMark != null
                  ? Value(course.passMark!)
                  : const Value.absent(),
              lecturers: course.lecturers.isNotEmpty
                  ? Value(course.lecturers.join(','))
                  : const Value.absent(),
            ),
          ),
          mode: InsertMode.insertOrReplace,
        );
        // Use upsert for course registrations
        batch.insertAll(
          _db.courseRegistrationsTable,
          regs.map((r) => CourseRegistrationsTableCompanion.insert(
                id: Value(r.id),
                courseId: r.course.id,
                sessionId: Value(r.session?.id),
                semesterAccronym: Value(r.semesterAccronym),
                semesterPosition: Value(r.semesterPosition),
                semesterTitle: Value(r.semesterTitle),
                score: Value(r.score?.toString()),
                courseStatus: Value(r.courseStatus),
                approvalStatus: Value(r.approvalStatus),
                approvalRemark: Value(r.approvalRemark),
                remark: Value(r.remark),
                createdAt: Value(r.createdAt),
                updatedAt: Value(r.updatedAt),
              )),
          mode: InsertMode.insertOrReplace,
        );
      });

      logger.i('Successfully saved ${regs.length} course registrations');
    } catch (e) {
      logger.e('Error saving course registrations: $e');
      rethrow;
    }
  }

  Future<List<CourseRegistration>> getCourseRegistrations(
      [int? sessionId]) async {
    try {
      final query = _db.select(_db.courseRegistrationsTable);
      if (sessionId != null) {
        query.where((tbl) => tbl.sessionId.equals(sessionId));
      }
      final regRows = await query.get();
      final courseIds = regRows.map((r) => r.courseId).toSet();
      final courseRows = await (_db.select(_db.coursesTable)
            ..where((tbl) => tbl.id.isIn(courseIds.toList())))
          .get();

      // Map courseId to Course
      final courses = {
        for (final c in courseRows)
          c.id: Course(
            id: c.id,
            courseCode: c.courseCode,
            courseTitle: c.courseTitle,
            creditUnit: c.creditUnit,
            courseSynopsis: c.courseSynopsis,
            type: c.type,
            host: Department(
              id: c.hostId ?? 0,
              name: '',
            ),
            department: Department(
              id: c.departmentId ?? 0,
              name: '',
            ),
            passMark: c.passMark,
            lecturers: (c.lecturers ?? '')
                .split(',')
                .where((e) => e.isNotEmpty)
                .toList(),
          )
      };

      final registrations = regRows
          .map((row) => CourseRegistration(
                id: row.id,
                course: courses[row.courseId]!,
                session: null,
                semesterAccronym: row.semesterAccronym,
                semesterPosition: row.semesterPosition,
                semesterTitle: row.semesterTitle,
                score: row.score,
                courseStatus: row.courseStatus,
                approvalStatus: row.approvalStatus,
                approvalRemark: row.approvalRemark,
                remark: row.remark,
                createdAt: row.createdAt,
                updatedAt: row.updatedAt,
              ))
          .toList();

      logger.i('Retrieved ${registrations.length} course registrations');
      return registrations;
    } catch (e) {
      logger.e('Error retrieving course registrations: $e');
      rethrow;
    }
  }

  Future<void> clearCourseRegistrations() async {
    try {
      await _db.batch((batch) {
        // Clear course registrations table
        batch.deleteAll(_db.courseRegistrationsTable);
        // Also clear courses table since they're related
        batch.deleteAll(_db.coursesTable);
      });
      logger.i(
          'Successfully cleared course registrations and courses from local database');
    } catch (e) {
      logger.e('Error clearing course registrations: $e');
      rethrow;
    }
  }
}
