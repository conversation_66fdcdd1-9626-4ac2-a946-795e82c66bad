// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_database.dart';

// ignore_for_file: type=lint
class $AnnouncementsTableTable extends AnnouncementsTable
    with TableInfo<$AnnouncementsTableTable, AnnouncementsTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AnnouncementsTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _messageMeta =
      const VerificationMeta('message');
  @override
  late final GeneratedColumn<String> message = GeneratedColumn<String>(
      'message', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _priorityMeta =
      const VerificationMeta('priority');
  @override
  late final GeneratedColumn<String> priority = GeneratedColumn<String>(
      'priority', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _announcerMeta =
      const VerificationMeta('announcer');
  @override
  late final GeneratedColumn<String> announcer = GeneratedColumn<String>(
      'announcer', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _recipientMeta =
      const VerificationMeta('recipient');
  @override
  late final GeneratedColumn<String> recipient = GeneratedColumn<String>(
      'recipient', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _levelMeta = const VerificationMeta('level');
  @override
  late final GeneratedColumn<String> level = GeneratedColumn<String>(
      'level', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _programmeLevelMeta =
      const VerificationMeta('programmeLevel');
  @override
  late final GeneratedColumn<String> programmeLevel = GeneratedColumn<String>(
      'programme_level', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        title,
        message,
        priority,
        announcer,
        recipient,
        level,
        programmeLevel,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'announcements_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<AnnouncementsTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('message')) {
      context.handle(_messageMeta,
          message.isAcceptableOrUnknown(data['message']!, _messageMeta));
    } else if (isInserting) {
      context.missing(_messageMeta);
    }
    if (data.containsKey('priority')) {
      context.handle(_priorityMeta,
          priority.isAcceptableOrUnknown(data['priority']!, _priorityMeta));
    } else if (isInserting) {
      context.missing(_priorityMeta);
    }
    if (data.containsKey('announcer')) {
      context.handle(_announcerMeta,
          announcer.isAcceptableOrUnknown(data['announcer']!, _announcerMeta));
    } else if (isInserting) {
      context.missing(_announcerMeta);
    }
    if (data.containsKey('recipient')) {
      context.handle(_recipientMeta,
          recipient.isAcceptableOrUnknown(data['recipient']!, _recipientMeta));
    } else if (isInserting) {
      context.missing(_recipientMeta);
    }
    if (data.containsKey('level')) {
      context.handle(
          _levelMeta, level.isAcceptableOrUnknown(data['level']!, _levelMeta));
    } else if (isInserting) {
      context.missing(_levelMeta);
    }
    if (data.containsKey('programme_level')) {
      context.handle(
          _programmeLevelMeta,
          programmeLevel.isAcceptableOrUnknown(
              data['programme_level']!, _programmeLevelMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AnnouncementsTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AnnouncementsTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      message: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}message'])!,
      priority: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}priority'])!,
      announcer: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}announcer'])!,
      recipient: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}recipient'])!,
      level: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}level'])!,
      programmeLevel: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}programme_level']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $AnnouncementsTableTable createAlias(String alias) {
    return $AnnouncementsTableTable(attachedDatabase, alias);
  }
}

class AnnouncementsTableData extends DataClass
    implements Insertable<AnnouncementsTableData> {
  final int id;
  final String title;
  final String message;
  final String priority;
  final String announcer;
  final String recipient;
  final String level;
  final String? programmeLevel;
  final DateTime createdAt;
  final DateTime updatedAt;
  const AnnouncementsTableData(
      {required this.id,
      required this.title,
      required this.message,
      required this.priority,
      required this.announcer,
      required this.recipient,
      required this.level,
      this.programmeLevel,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['title'] = Variable<String>(title);
    map['message'] = Variable<String>(message);
    map['priority'] = Variable<String>(priority);
    map['announcer'] = Variable<String>(announcer);
    map['recipient'] = Variable<String>(recipient);
    map['level'] = Variable<String>(level);
    if (!nullToAbsent || programmeLevel != null) {
      map['programme_level'] = Variable<String>(programmeLevel);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  AnnouncementsTableCompanion toCompanion(bool nullToAbsent) {
    return AnnouncementsTableCompanion(
      id: Value(id),
      title: Value(title),
      message: Value(message),
      priority: Value(priority),
      announcer: Value(announcer),
      recipient: Value(recipient),
      level: Value(level),
      programmeLevel: programmeLevel == null && nullToAbsent
          ? const Value.absent()
          : Value(programmeLevel),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory AnnouncementsTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AnnouncementsTableData(
      id: serializer.fromJson<int>(json['id']),
      title: serializer.fromJson<String>(json['title']),
      message: serializer.fromJson<String>(json['message']),
      priority: serializer.fromJson<String>(json['priority']),
      announcer: serializer.fromJson<String>(json['announcer']),
      recipient: serializer.fromJson<String>(json['recipient']),
      level: serializer.fromJson<String>(json['level']),
      programmeLevel: serializer.fromJson<String?>(json['programmeLevel']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'title': serializer.toJson<String>(title),
      'message': serializer.toJson<String>(message),
      'priority': serializer.toJson<String>(priority),
      'announcer': serializer.toJson<String>(announcer),
      'recipient': serializer.toJson<String>(recipient),
      'level': serializer.toJson<String>(level),
      'programmeLevel': serializer.toJson<String?>(programmeLevel),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  AnnouncementsTableData copyWith(
          {int? id,
          String? title,
          String? message,
          String? priority,
          String? announcer,
          String? recipient,
          String? level,
          Value<String?> programmeLevel = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      AnnouncementsTableData(
        id: id ?? this.id,
        title: title ?? this.title,
        message: message ?? this.message,
        priority: priority ?? this.priority,
        announcer: announcer ?? this.announcer,
        recipient: recipient ?? this.recipient,
        level: level ?? this.level,
        programmeLevel:
            programmeLevel.present ? programmeLevel.value : this.programmeLevel,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  AnnouncementsTableData copyWithCompanion(AnnouncementsTableCompanion data) {
    return AnnouncementsTableData(
      id: data.id.present ? data.id.value : this.id,
      title: data.title.present ? data.title.value : this.title,
      message: data.message.present ? data.message.value : this.message,
      priority: data.priority.present ? data.priority.value : this.priority,
      announcer: data.announcer.present ? data.announcer.value : this.announcer,
      recipient: data.recipient.present ? data.recipient.value : this.recipient,
      level: data.level.present ? data.level.value : this.level,
      programmeLevel: data.programmeLevel.present
          ? data.programmeLevel.value
          : this.programmeLevel,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AnnouncementsTableData(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('message: $message, ')
          ..write('priority: $priority, ')
          ..write('announcer: $announcer, ')
          ..write('recipient: $recipient, ')
          ..write('level: $level, ')
          ..write('programmeLevel: $programmeLevel, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, title, message, priority, announcer,
      recipient, level, programmeLevel, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AnnouncementsTableData &&
          other.id == this.id &&
          other.title == this.title &&
          other.message == this.message &&
          other.priority == this.priority &&
          other.announcer == this.announcer &&
          other.recipient == this.recipient &&
          other.level == this.level &&
          other.programmeLevel == this.programmeLevel &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class AnnouncementsTableCompanion
    extends UpdateCompanion<AnnouncementsTableData> {
  final Value<int> id;
  final Value<String> title;
  final Value<String> message;
  final Value<String> priority;
  final Value<String> announcer;
  final Value<String> recipient;
  final Value<String> level;
  final Value<String?> programmeLevel;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  const AnnouncementsTableCompanion({
    this.id = const Value.absent(),
    this.title = const Value.absent(),
    this.message = const Value.absent(),
    this.priority = const Value.absent(),
    this.announcer = const Value.absent(),
    this.recipient = const Value.absent(),
    this.level = const Value.absent(),
    this.programmeLevel = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  AnnouncementsTableCompanion.insert({
    this.id = const Value.absent(),
    required String title,
    required String message,
    required String priority,
    required String announcer,
    required String recipient,
    required String level,
    this.programmeLevel = const Value.absent(),
    required DateTime createdAt,
    required DateTime updatedAt,
  })  : title = Value(title),
        message = Value(message),
        priority = Value(priority),
        announcer = Value(announcer),
        recipient = Value(recipient),
        level = Value(level),
        createdAt = Value(createdAt),
        updatedAt = Value(updatedAt);
  static Insertable<AnnouncementsTableData> custom({
    Expression<int>? id,
    Expression<String>? title,
    Expression<String>? message,
    Expression<String>? priority,
    Expression<String>? announcer,
    Expression<String>? recipient,
    Expression<String>? level,
    Expression<String>? programmeLevel,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (message != null) 'message': message,
      if (priority != null) 'priority': priority,
      if (announcer != null) 'announcer': announcer,
      if (recipient != null) 'recipient': recipient,
      if (level != null) 'level': level,
      if (programmeLevel != null) 'programme_level': programmeLevel,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  AnnouncementsTableCompanion copyWith(
      {Value<int>? id,
      Value<String>? title,
      Value<String>? message,
      Value<String>? priority,
      Value<String>? announcer,
      Value<String>? recipient,
      Value<String>? level,
      Value<String?>? programmeLevel,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt}) {
    return AnnouncementsTableCompanion(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      priority: priority ?? this.priority,
      announcer: announcer ?? this.announcer,
      recipient: recipient ?? this.recipient,
      level: level ?? this.level,
      programmeLevel: programmeLevel ?? this.programmeLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (message.present) {
      map['message'] = Variable<String>(message.value);
    }
    if (priority.present) {
      map['priority'] = Variable<String>(priority.value);
    }
    if (announcer.present) {
      map['announcer'] = Variable<String>(announcer.value);
    }
    if (recipient.present) {
      map['recipient'] = Variable<String>(recipient.value);
    }
    if (level.present) {
      map['level'] = Variable<String>(level.value);
    }
    if (programmeLevel.present) {
      map['programme_level'] = Variable<String>(programmeLevel.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AnnouncementsTableCompanion(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('message: $message, ')
          ..write('priority: $priority, ')
          ..write('announcer: $announcer, ')
          ..write('recipient: $recipient, ')
          ..write('level: $level, ')
          ..write('programmeLevel: $programmeLevel, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

class $SessionsTableTable extends SessionsTable
    with TableInfo<$SessionsTableTable, SessionsTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SessionsTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _sessionNameMeta =
      const VerificationMeta('sessionName');
  @override
  late final GeneratedColumn<String> sessionName = GeneratedColumn<String>(
      'session_name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _startDateMeta =
      const VerificationMeta('startDate');
  @override
  late final GeneratedColumn<String> startDate = GeneratedColumn<String>(
      'start_date', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _endDateMeta =
      const VerificationMeta('endDate');
  @override
  late final GeneratedColumn<String> endDate = GeneratedColumn<String>(
      'end_date', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _registrationPaymentStatusMeta =
      const VerificationMeta('registrationPaymentStatus');
  @override
  late final GeneratedColumn<bool> registrationPaymentStatus =
      GeneratedColumn<bool>('registration_payment_status', aliasedName, true,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("registration_payment_status" IN (0, 1))'));
  static const VerificationMeta _transferRequestStatusMeta =
      const VerificationMeta('transferRequestStatus');
  @override
  late final GeneratedColumn<bool> transferRequestStatus =
      GeneratedColumn<bool>('transfer_request_status', aliasedName, true,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("transfer_request_status" IN (0, 1))'));
  static const VerificationMeta _courseRegistrationStatusMeta =
      const VerificationMeta('courseRegistrationStatus');
  @override
  late final GeneratedColumn<bool> courseRegistrationStatus =
      GeneratedColumn<bool>(
          'course_registration_status', aliasedName, true,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("course_registration_status" IN (0, 1))'));
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _positionMeta =
      const VerificationMeta('position');
  @override
  late final GeneratedColumn<int> position = GeneratedColumn<int>(
      'position', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<String> createdAt = GeneratedColumn<String>(
      'created_at', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<String> updatedAt = GeneratedColumn<String>(
      'updated_at', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _registrationPaymentClosingDateMeta =
      const VerificationMeta('registrationPaymentClosingDate');
  @override
  late final GeneratedColumn<String> registrationPaymentClosingDate =
      GeneratedColumn<String>(
          'registration_payment_closing_date', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _courseRegistrationClosingDateMeta =
      const VerificationMeta('courseRegistrationClosingDate');
  @override
  late final GeneratedColumn<String> courseRegistrationClosingDate =
      GeneratedColumn<String>(
          'course_registration_closing_date', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        sessionName,
        startDate,
        endDate,
        registrationPaymentStatus,
        transferRequestStatus,
        courseRegistrationStatus,
        status,
        position,
        createdAt,
        updatedAt,
        registrationPaymentClosingDate,
        courseRegistrationClosingDate
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'sessions_table';
  @override
  VerificationContext validateIntegrity(Insertable<SessionsTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('session_name')) {
      context.handle(
          _sessionNameMeta,
          sessionName.isAcceptableOrUnknown(
              data['session_name']!, _sessionNameMeta));
    } else if (isInserting) {
      context.missing(_sessionNameMeta);
    }
    if (data.containsKey('start_date')) {
      context.handle(_startDateMeta,
          startDate.isAcceptableOrUnknown(data['start_date']!, _startDateMeta));
    }
    if (data.containsKey('end_date')) {
      context.handle(_endDateMeta,
          endDate.isAcceptableOrUnknown(data['end_date']!, _endDateMeta));
    }
    if (data.containsKey('registration_payment_status')) {
      context.handle(
          _registrationPaymentStatusMeta,
          registrationPaymentStatus.isAcceptableOrUnknown(
              data['registration_payment_status']!,
              _registrationPaymentStatusMeta));
    }
    if (data.containsKey('transfer_request_status')) {
      context.handle(
          _transferRequestStatusMeta,
          transferRequestStatus.isAcceptableOrUnknown(
              data['transfer_request_status']!, _transferRequestStatusMeta));
    }
    if (data.containsKey('course_registration_status')) {
      context.handle(
          _courseRegistrationStatusMeta,
          courseRegistrationStatus.isAcceptableOrUnknown(
              data['course_registration_status']!,
              _courseRegistrationStatusMeta));
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    }
    if (data.containsKey('position')) {
      context.handle(_positionMeta,
          position.isAcceptableOrUnknown(data['position']!, _positionMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    if (data.containsKey('registration_payment_closing_date')) {
      context.handle(
          _registrationPaymentClosingDateMeta,
          registrationPaymentClosingDate.isAcceptableOrUnknown(
              data['registration_payment_closing_date']!,
              _registrationPaymentClosingDateMeta));
    }
    if (data.containsKey('course_registration_closing_date')) {
      context.handle(
          _courseRegistrationClosingDateMeta,
          courseRegistrationClosingDate.isAcceptableOrUnknown(
              data['course_registration_closing_date']!,
              _courseRegistrationClosingDateMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SessionsTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SessionsTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      sessionName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}session_name'])!,
      startDate: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}start_date']),
      endDate: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}end_date']),
      registrationPaymentStatus: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}registration_payment_status']),
      transferRequestStatus: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}transfer_request_status']),
      courseRegistrationStatus: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}course_registration_status']),
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status']),
      position: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}position']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}created_at']),
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}updated_at']),
      registrationPaymentClosingDate: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}registration_payment_closing_date']),
      courseRegistrationClosingDate: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}course_registration_closing_date']),
    );
  }

  @override
  $SessionsTableTable createAlias(String alias) {
    return $SessionsTableTable(attachedDatabase, alias);
  }
}

class SessionsTableData extends DataClass
    implements Insertable<SessionsTableData> {
  final int id;
  final String sessionName;
  final String? startDate;
  final String? endDate;
  final bool? registrationPaymentStatus;
  final bool? transferRequestStatus;
  final bool? courseRegistrationStatus;
  final String? status;
  final int? position;
  final String? createdAt;
  final String? updatedAt;
  final String? registrationPaymentClosingDate;
  final String? courseRegistrationClosingDate;
  const SessionsTableData(
      {required this.id,
      required this.sessionName,
      this.startDate,
      this.endDate,
      this.registrationPaymentStatus,
      this.transferRequestStatus,
      this.courseRegistrationStatus,
      this.status,
      this.position,
      this.createdAt,
      this.updatedAt,
      this.registrationPaymentClosingDate,
      this.courseRegistrationClosingDate});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['session_name'] = Variable<String>(sessionName);
    if (!nullToAbsent || startDate != null) {
      map['start_date'] = Variable<String>(startDate);
    }
    if (!nullToAbsent || endDate != null) {
      map['end_date'] = Variable<String>(endDate);
    }
    if (!nullToAbsent || registrationPaymentStatus != null) {
      map['registration_payment_status'] =
          Variable<bool>(registrationPaymentStatus);
    }
    if (!nullToAbsent || transferRequestStatus != null) {
      map['transfer_request_status'] = Variable<bool>(transferRequestStatus);
    }
    if (!nullToAbsent || courseRegistrationStatus != null) {
      map['course_registration_status'] =
          Variable<bool>(courseRegistrationStatus);
    }
    if (!nullToAbsent || status != null) {
      map['status'] = Variable<String>(status);
    }
    if (!nullToAbsent || position != null) {
      map['position'] = Variable<int>(position);
    }
    if (!nullToAbsent || createdAt != null) {
      map['created_at'] = Variable<String>(createdAt);
    }
    if (!nullToAbsent || updatedAt != null) {
      map['updated_at'] = Variable<String>(updatedAt);
    }
    if (!nullToAbsent || registrationPaymentClosingDate != null) {
      map['registration_payment_closing_date'] =
          Variable<String>(registrationPaymentClosingDate);
    }
    if (!nullToAbsent || courseRegistrationClosingDate != null) {
      map['course_registration_closing_date'] =
          Variable<String>(courseRegistrationClosingDate);
    }
    return map;
  }

  SessionsTableCompanion toCompanion(bool nullToAbsent) {
    return SessionsTableCompanion(
      id: Value(id),
      sessionName: Value(sessionName),
      startDate: startDate == null && nullToAbsent
          ? const Value.absent()
          : Value(startDate),
      endDate: endDate == null && nullToAbsent
          ? const Value.absent()
          : Value(endDate),
      registrationPaymentStatus:
          registrationPaymentStatus == null && nullToAbsent
              ? const Value.absent()
              : Value(registrationPaymentStatus),
      transferRequestStatus: transferRequestStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(transferRequestStatus),
      courseRegistrationStatus: courseRegistrationStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(courseRegistrationStatus),
      status:
          status == null && nullToAbsent ? const Value.absent() : Value(status),
      position: position == null && nullToAbsent
          ? const Value.absent()
          : Value(position),
      createdAt: createdAt == null && nullToAbsent
          ? const Value.absent()
          : Value(createdAt),
      updatedAt: updatedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(updatedAt),
      registrationPaymentClosingDate:
          registrationPaymentClosingDate == null && nullToAbsent
              ? const Value.absent()
              : Value(registrationPaymentClosingDate),
      courseRegistrationClosingDate:
          courseRegistrationClosingDate == null && nullToAbsent
              ? const Value.absent()
              : Value(courseRegistrationClosingDate),
    );
  }

  factory SessionsTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SessionsTableData(
      id: serializer.fromJson<int>(json['id']),
      sessionName: serializer.fromJson<String>(json['sessionName']),
      startDate: serializer.fromJson<String?>(json['startDate']),
      endDate: serializer.fromJson<String?>(json['endDate']),
      registrationPaymentStatus:
          serializer.fromJson<bool?>(json['registrationPaymentStatus']),
      transferRequestStatus:
          serializer.fromJson<bool?>(json['transferRequestStatus']),
      courseRegistrationStatus:
          serializer.fromJson<bool?>(json['courseRegistrationStatus']),
      status: serializer.fromJson<String?>(json['status']),
      position: serializer.fromJson<int?>(json['position']),
      createdAt: serializer.fromJson<String?>(json['createdAt']),
      updatedAt: serializer.fromJson<String?>(json['updatedAt']),
      registrationPaymentClosingDate:
          serializer.fromJson<String?>(json['registrationPaymentClosingDate']),
      courseRegistrationClosingDate:
          serializer.fromJson<String?>(json['courseRegistrationClosingDate']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'sessionName': serializer.toJson<String>(sessionName),
      'startDate': serializer.toJson<String?>(startDate),
      'endDate': serializer.toJson<String?>(endDate),
      'registrationPaymentStatus':
          serializer.toJson<bool?>(registrationPaymentStatus),
      'transferRequestStatus': serializer.toJson<bool?>(transferRequestStatus),
      'courseRegistrationStatus':
          serializer.toJson<bool?>(courseRegistrationStatus),
      'status': serializer.toJson<String?>(status),
      'position': serializer.toJson<int?>(position),
      'createdAt': serializer.toJson<String?>(createdAt),
      'updatedAt': serializer.toJson<String?>(updatedAt),
      'registrationPaymentClosingDate':
          serializer.toJson<String?>(registrationPaymentClosingDate),
      'courseRegistrationClosingDate':
          serializer.toJson<String?>(courseRegistrationClosingDate),
    };
  }

  SessionsTableData copyWith(
          {int? id,
          String? sessionName,
          Value<String?> startDate = const Value.absent(),
          Value<String?> endDate = const Value.absent(),
          Value<bool?> registrationPaymentStatus = const Value.absent(),
          Value<bool?> transferRequestStatus = const Value.absent(),
          Value<bool?> courseRegistrationStatus = const Value.absent(),
          Value<String?> status = const Value.absent(),
          Value<int?> position = const Value.absent(),
          Value<String?> createdAt = const Value.absent(),
          Value<String?> updatedAt = const Value.absent(),
          Value<String?> registrationPaymentClosingDate = const Value.absent(),
          Value<String?> courseRegistrationClosingDate =
              const Value.absent()}) =>
      SessionsTableData(
        id: id ?? this.id,
        sessionName: sessionName ?? this.sessionName,
        startDate: startDate.present ? startDate.value : this.startDate,
        endDate: endDate.present ? endDate.value : this.endDate,
        registrationPaymentStatus: registrationPaymentStatus.present
            ? registrationPaymentStatus.value
            : this.registrationPaymentStatus,
        transferRequestStatus: transferRequestStatus.present
            ? transferRequestStatus.value
            : this.transferRequestStatus,
        courseRegistrationStatus: courseRegistrationStatus.present
            ? courseRegistrationStatus.value
            : this.courseRegistrationStatus,
        status: status.present ? status.value : this.status,
        position: position.present ? position.value : this.position,
        createdAt: createdAt.present ? createdAt.value : this.createdAt,
        updatedAt: updatedAt.present ? updatedAt.value : this.updatedAt,
        registrationPaymentClosingDate: registrationPaymentClosingDate.present
            ? registrationPaymentClosingDate.value
            : this.registrationPaymentClosingDate,
        courseRegistrationClosingDate: courseRegistrationClosingDate.present
            ? courseRegistrationClosingDate.value
            : this.courseRegistrationClosingDate,
      );
  SessionsTableData copyWithCompanion(SessionsTableCompanion data) {
    return SessionsTableData(
      id: data.id.present ? data.id.value : this.id,
      sessionName:
          data.sessionName.present ? data.sessionName.value : this.sessionName,
      startDate: data.startDate.present ? data.startDate.value : this.startDate,
      endDate: data.endDate.present ? data.endDate.value : this.endDate,
      registrationPaymentStatus: data.registrationPaymentStatus.present
          ? data.registrationPaymentStatus.value
          : this.registrationPaymentStatus,
      transferRequestStatus: data.transferRequestStatus.present
          ? data.transferRequestStatus.value
          : this.transferRequestStatus,
      courseRegistrationStatus: data.courseRegistrationStatus.present
          ? data.courseRegistrationStatus.value
          : this.courseRegistrationStatus,
      status: data.status.present ? data.status.value : this.status,
      position: data.position.present ? data.position.value : this.position,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      registrationPaymentClosingDate:
          data.registrationPaymentClosingDate.present
              ? data.registrationPaymentClosingDate.value
              : this.registrationPaymentClosingDate,
      courseRegistrationClosingDate: data.courseRegistrationClosingDate.present
          ? data.courseRegistrationClosingDate.value
          : this.courseRegistrationClosingDate,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SessionsTableData(')
          ..write('id: $id, ')
          ..write('sessionName: $sessionName, ')
          ..write('startDate: $startDate, ')
          ..write('endDate: $endDate, ')
          ..write('registrationPaymentStatus: $registrationPaymentStatus, ')
          ..write('transferRequestStatus: $transferRequestStatus, ')
          ..write('courseRegistrationStatus: $courseRegistrationStatus, ')
          ..write('status: $status, ')
          ..write('position: $position, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write(
              'registrationPaymentClosingDate: $registrationPaymentClosingDate, ')
          ..write(
              'courseRegistrationClosingDate: $courseRegistrationClosingDate')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      sessionName,
      startDate,
      endDate,
      registrationPaymentStatus,
      transferRequestStatus,
      courseRegistrationStatus,
      status,
      position,
      createdAt,
      updatedAt,
      registrationPaymentClosingDate,
      courseRegistrationClosingDate);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SessionsTableData &&
          other.id == this.id &&
          other.sessionName == this.sessionName &&
          other.startDate == this.startDate &&
          other.endDate == this.endDate &&
          other.registrationPaymentStatus == this.registrationPaymentStatus &&
          other.transferRequestStatus == this.transferRequestStatus &&
          other.courseRegistrationStatus == this.courseRegistrationStatus &&
          other.status == this.status &&
          other.position == this.position &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.registrationPaymentClosingDate ==
              this.registrationPaymentClosingDate &&
          other.courseRegistrationClosingDate ==
              this.courseRegistrationClosingDate);
}

class SessionsTableCompanion extends UpdateCompanion<SessionsTableData> {
  final Value<int> id;
  final Value<String> sessionName;
  final Value<String?> startDate;
  final Value<String?> endDate;
  final Value<bool?> registrationPaymentStatus;
  final Value<bool?> transferRequestStatus;
  final Value<bool?> courseRegistrationStatus;
  final Value<String?> status;
  final Value<int?> position;
  final Value<String?> createdAt;
  final Value<String?> updatedAt;
  final Value<String?> registrationPaymentClosingDate;
  final Value<String?> courseRegistrationClosingDate;
  const SessionsTableCompanion({
    this.id = const Value.absent(),
    this.sessionName = const Value.absent(),
    this.startDate = const Value.absent(),
    this.endDate = const Value.absent(),
    this.registrationPaymentStatus = const Value.absent(),
    this.transferRequestStatus = const Value.absent(),
    this.courseRegistrationStatus = const Value.absent(),
    this.status = const Value.absent(),
    this.position = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.registrationPaymentClosingDate = const Value.absent(),
    this.courseRegistrationClosingDate = const Value.absent(),
  });
  SessionsTableCompanion.insert({
    this.id = const Value.absent(),
    required String sessionName,
    this.startDate = const Value.absent(),
    this.endDate = const Value.absent(),
    this.registrationPaymentStatus = const Value.absent(),
    this.transferRequestStatus = const Value.absent(),
    this.courseRegistrationStatus = const Value.absent(),
    this.status = const Value.absent(),
    this.position = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.registrationPaymentClosingDate = const Value.absent(),
    this.courseRegistrationClosingDate = const Value.absent(),
  }) : sessionName = Value(sessionName);
  static Insertable<SessionsTableData> custom({
    Expression<int>? id,
    Expression<String>? sessionName,
    Expression<String>? startDate,
    Expression<String>? endDate,
    Expression<bool>? registrationPaymentStatus,
    Expression<bool>? transferRequestStatus,
    Expression<bool>? courseRegistrationStatus,
    Expression<String>? status,
    Expression<int>? position,
    Expression<String>? createdAt,
    Expression<String>? updatedAt,
    Expression<String>? registrationPaymentClosingDate,
    Expression<String>? courseRegistrationClosingDate,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (sessionName != null) 'session_name': sessionName,
      if (startDate != null) 'start_date': startDate,
      if (endDate != null) 'end_date': endDate,
      if (registrationPaymentStatus != null)
        'registration_payment_status': registrationPaymentStatus,
      if (transferRequestStatus != null)
        'transfer_request_status': transferRequestStatus,
      if (courseRegistrationStatus != null)
        'course_registration_status': courseRegistrationStatus,
      if (status != null) 'status': status,
      if (position != null) 'position': position,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (registrationPaymentClosingDate != null)
        'registration_payment_closing_date': registrationPaymentClosingDate,
      if (courseRegistrationClosingDate != null)
        'course_registration_closing_date': courseRegistrationClosingDate,
    });
  }

  SessionsTableCompanion copyWith(
      {Value<int>? id,
      Value<String>? sessionName,
      Value<String?>? startDate,
      Value<String?>? endDate,
      Value<bool?>? registrationPaymentStatus,
      Value<bool?>? transferRequestStatus,
      Value<bool?>? courseRegistrationStatus,
      Value<String?>? status,
      Value<int?>? position,
      Value<String?>? createdAt,
      Value<String?>? updatedAt,
      Value<String?>? registrationPaymentClosingDate,
      Value<String?>? courseRegistrationClosingDate}) {
    return SessionsTableCompanion(
      id: id ?? this.id,
      sessionName: sessionName ?? this.sessionName,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      registrationPaymentStatus:
          registrationPaymentStatus ?? this.registrationPaymentStatus,
      transferRequestStatus:
          transferRequestStatus ?? this.transferRequestStatus,
      courseRegistrationStatus:
          courseRegistrationStatus ?? this.courseRegistrationStatus,
      status: status ?? this.status,
      position: position ?? this.position,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      registrationPaymentClosingDate:
          registrationPaymentClosingDate ?? this.registrationPaymentClosingDate,
      courseRegistrationClosingDate:
          courseRegistrationClosingDate ?? this.courseRegistrationClosingDate,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (sessionName.present) {
      map['session_name'] = Variable<String>(sessionName.value);
    }
    if (startDate.present) {
      map['start_date'] = Variable<String>(startDate.value);
    }
    if (endDate.present) {
      map['end_date'] = Variable<String>(endDate.value);
    }
    if (registrationPaymentStatus.present) {
      map['registration_payment_status'] =
          Variable<bool>(registrationPaymentStatus.value);
    }
    if (transferRequestStatus.present) {
      map['transfer_request_status'] =
          Variable<bool>(transferRequestStatus.value);
    }
    if (courseRegistrationStatus.present) {
      map['course_registration_status'] =
          Variable<bool>(courseRegistrationStatus.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (position.present) {
      map['position'] = Variable<int>(position.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<String>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<String>(updatedAt.value);
    }
    if (registrationPaymentClosingDate.present) {
      map['registration_payment_closing_date'] =
          Variable<String>(registrationPaymentClosingDate.value);
    }
    if (courseRegistrationClosingDate.present) {
      map['course_registration_closing_date'] =
          Variable<String>(courseRegistrationClosingDate.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SessionsTableCompanion(')
          ..write('id: $id, ')
          ..write('sessionName: $sessionName, ')
          ..write('startDate: $startDate, ')
          ..write('endDate: $endDate, ')
          ..write('registrationPaymentStatus: $registrationPaymentStatus, ')
          ..write('transferRequestStatus: $transferRequestStatus, ')
          ..write('courseRegistrationStatus: $courseRegistrationStatus, ')
          ..write('status: $status, ')
          ..write('position: $position, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write(
              'registrationPaymentClosingDate: $registrationPaymentClosingDate, ')
          ..write(
              'courseRegistrationClosingDate: $courseRegistrationClosingDate')
          ..write(')'))
        .toString();
  }
}

class $SemestersTableTable extends SemestersTable
    with TableInfo<$SemestersTableTable, SemestersTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SemestersTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _sessionIdMeta =
      const VerificationMeta('sessionId');
  @override
  late final GeneratedColumn<int> sessionId = GeneratedColumn<int>(
      'session_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _accronymMeta =
      const VerificationMeta('accronym');
  @override
  late final GeneratedColumn<String> accronym = GeneratedColumn<String>(
      'accronym', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _positionMeta =
      const VerificationMeta('position');
  @override
  late final GeneratedColumn<int> position = GeneratedColumn<int>(
      'position', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _startDateMeta =
      const VerificationMeta('startDate');
  @override
  late final GeneratedColumn<String> startDate = GeneratedColumn<String>(
      'start_date', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _endDateMeta =
      const VerificationMeta('endDate');
  @override
  late final GeneratedColumn<String> endDate = GeneratedColumn<String>(
      'end_date', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _lateRegistrationStartDateMeta =
      const VerificationMeta('lateRegistrationStartDate');
  @override
  late final GeneratedColumn<String> lateRegistrationStartDate =
      GeneratedColumn<String>('late_registration_start_date', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _statusTextMeta =
      const VerificationMeta('statusText');
  @override
  late final GeneratedColumn<String> statusText = GeneratedColumn<String>(
      'status_text', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _semesterSettingIdMeta =
      const VerificationMeta('semesterSettingId');
  @override
  late final GeneratedColumn<int> semesterSettingId = GeneratedColumn<int>(
      'semester_setting_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        sessionId,
        title,
        accronym,
        position,
        startDate,
        endDate,
        lateRegistrationStartDate,
        statusText,
        semesterSettingId
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'semesters_table';
  @override
  VerificationContext validateIntegrity(Insertable<SemestersTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('session_id')) {
      context.handle(_sessionIdMeta,
          sessionId.isAcceptableOrUnknown(data['session_id']!, _sessionIdMeta));
    } else if (isInserting) {
      context.missing(_sessionIdMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    }
    if (data.containsKey('accronym')) {
      context.handle(_accronymMeta,
          accronym.isAcceptableOrUnknown(data['accronym']!, _accronymMeta));
    }
    if (data.containsKey('position')) {
      context.handle(_positionMeta,
          position.isAcceptableOrUnknown(data['position']!, _positionMeta));
    }
    if (data.containsKey('start_date')) {
      context.handle(_startDateMeta,
          startDate.isAcceptableOrUnknown(data['start_date']!, _startDateMeta));
    }
    if (data.containsKey('end_date')) {
      context.handle(_endDateMeta,
          endDate.isAcceptableOrUnknown(data['end_date']!, _endDateMeta));
    }
    if (data.containsKey('late_registration_start_date')) {
      context.handle(
          _lateRegistrationStartDateMeta,
          lateRegistrationStartDate.isAcceptableOrUnknown(
              data['late_registration_start_date']!,
              _lateRegistrationStartDateMeta));
    }
    if (data.containsKey('status_text')) {
      context.handle(
          _statusTextMeta,
          statusText.isAcceptableOrUnknown(
              data['status_text']!, _statusTextMeta));
    }
    if (data.containsKey('semester_setting_id')) {
      context.handle(
          _semesterSettingIdMeta,
          semesterSettingId.isAcceptableOrUnknown(
              data['semester_setting_id']!, _semesterSettingIdMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SemestersTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SemestersTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      sessionId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}session_id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title']),
      accronym: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}accronym']),
      position: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}position']),
      startDate: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}start_date']),
      endDate: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}end_date']),
      lateRegistrationStartDate: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}late_registration_start_date']),
      statusText: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status_text']),
      semesterSettingId: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}semester_setting_id']),
    );
  }

  @override
  $SemestersTableTable createAlias(String alias) {
    return $SemestersTableTable(attachedDatabase, alias);
  }
}

class SemestersTableData extends DataClass
    implements Insertable<SemestersTableData> {
  final int id;
  final int sessionId;
  final String? title;
  final String? accronym;
  final int? position;
  final String? startDate;
  final String? endDate;
  final String? lateRegistrationStartDate;
  final String? statusText;
  final int? semesterSettingId;
  const SemestersTableData(
      {required this.id,
      required this.sessionId,
      this.title,
      this.accronym,
      this.position,
      this.startDate,
      this.endDate,
      this.lateRegistrationStartDate,
      this.statusText,
      this.semesterSettingId});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['session_id'] = Variable<int>(sessionId);
    if (!nullToAbsent || title != null) {
      map['title'] = Variable<String>(title);
    }
    if (!nullToAbsent || accronym != null) {
      map['accronym'] = Variable<String>(accronym);
    }
    if (!nullToAbsent || position != null) {
      map['position'] = Variable<int>(position);
    }
    if (!nullToAbsent || startDate != null) {
      map['start_date'] = Variable<String>(startDate);
    }
    if (!nullToAbsent || endDate != null) {
      map['end_date'] = Variable<String>(endDate);
    }
    if (!nullToAbsent || lateRegistrationStartDate != null) {
      map['late_registration_start_date'] =
          Variable<String>(lateRegistrationStartDate);
    }
    if (!nullToAbsent || statusText != null) {
      map['status_text'] = Variable<String>(statusText);
    }
    if (!nullToAbsent || semesterSettingId != null) {
      map['semester_setting_id'] = Variable<int>(semesterSettingId);
    }
    return map;
  }

  SemestersTableCompanion toCompanion(bool nullToAbsent) {
    return SemestersTableCompanion(
      id: Value(id),
      sessionId: Value(sessionId),
      title:
          title == null && nullToAbsent ? const Value.absent() : Value(title),
      accronym: accronym == null && nullToAbsent
          ? const Value.absent()
          : Value(accronym),
      position: position == null && nullToAbsent
          ? const Value.absent()
          : Value(position),
      startDate: startDate == null && nullToAbsent
          ? const Value.absent()
          : Value(startDate),
      endDate: endDate == null && nullToAbsent
          ? const Value.absent()
          : Value(endDate),
      lateRegistrationStartDate:
          lateRegistrationStartDate == null && nullToAbsent
              ? const Value.absent()
              : Value(lateRegistrationStartDate),
      statusText: statusText == null && nullToAbsent
          ? const Value.absent()
          : Value(statusText),
      semesterSettingId: semesterSettingId == null && nullToAbsent
          ? const Value.absent()
          : Value(semesterSettingId),
    );
  }

  factory SemestersTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SemestersTableData(
      id: serializer.fromJson<int>(json['id']),
      sessionId: serializer.fromJson<int>(json['sessionId']),
      title: serializer.fromJson<String?>(json['title']),
      accronym: serializer.fromJson<String?>(json['accronym']),
      position: serializer.fromJson<int?>(json['position']),
      startDate: serializer.fromJson<String?>(json['startDate']),
      endDate: serializer.fromJson<String?>(json['endDate']),
      lateRegistrationStartDate:
          serializer.fromJson<String?>(json['lateRegistrationStartDate']),
      statusText: serializer.fromJson<String?>(json['statusText']),
      semesterSettingId: serializer.fromJson<int?>(json['semesterSettingId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'sessionId': serializer.toJson<int>(sessionId),
      'title': serializer.toJson<String?>(title),
      'accronym': serializer.toJson<String?>(accronym),
      'position': serializer.toJson<int?>(position),
      'startDate': serializer.toJson<String?>(startDate),
      'endDate': serializer.toJson<String?>(endDate),
      'lateRegistrationStartDate':
          serializer.toJson<String?>(lateRegistrationStartDate),
      'statusText': serializer.toJson<String?>(statusText),
      'semesterSettingId': serializer.toJson<int?>(semesterSettingId),
    };
  }

  SemestersTableData copyWith(
          {int? id,
          int? sessionId,
          Value<String?> title = const Value.absent(),
          Value<String?> accronym = const Value.absent(),
          Value<int?> position = const Value.absent(),
          Value<String?> startDate = const Value.absent(),
          Value<String?> endDate = const Value.absent(),
          Value<String?> lateRegistrationStartDate = const Value.absent(),
          Value<String?> statusText = const Value.absent(),
          Value<int?> semesterSettingId = const Value.absent()}) =>
      SemestersTableData(
        id: id ?? this.id,
        sessionId: sessionId ?? this.sessionId,
        title: title.present ? title.value : this.title,
        accronym: accronym.present ? accronym.value : this.accronym,
        position: position.present ? position.value : this.position,
        startDate: startDate.present ? startDate.value : this.startDate,
        endDate: endDate.present ? endDate.value : this.endDate,
        lateRegistrationStartDate: lateRegistrationStartDate.present
            ? lateRegistrationStartDate.value
            : this.lateRegistrationStartDate,
        statusText: statusText.present ? statusText.value : this.statusText,
        semesterSettingId: semesterSettingId.present
            ? semesterSettingId.value
            : this.semesterSettingId,
      );
  SemestersTableData copyWithCompanion(SemestersTableCompanion data) {
    return SemestersTableData(
      id: data.id.present ? data.id.value : this.id,
      sessionId: data.sessionId.present ? data.sessionId.value : this.sessionId,
      title: data.title.present ? data.title.value : this.title,
      accronym: data.accronym.present ? data.accronym.value : this.accronym,
      position: data.position.present ? data.position.value : this.position,
      startDate: data.startDate.present ? data.startDate.value : this.startDate,
      endDate: data.endDate.present ? data.endDate.value : this.endDate,
      lateRegistrationStartDate: data.lateRegistrationStartDate.present
          ? data.lateRegistrationStartDate.value
          : this.lateRegistrationStartDate,
      statusText:
          data.statusText.present ? data.statusText.value : this.statusText,
      semesterSettingId: data.semesterSettingId.present
          ? data.semesterSettingId.value
          : this.semesterSettingId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SemestersTableData(')
          ..write('id: $id, ')
          ..write('sessionId: $sessionId, ')
          ..write('title: $title, ')
          ..write('accronym: $accronym, ')
          ..write('position: $position, ')
          ..write('startDate: $startDate, ')
          ..write('endDate: $endDate, ')
          ..write('lateRegistrationStartDate: $lateRegistrationStartDate, ')
          ..write('statusText: $statusText, ')
          ..write('semesterSettingId: $semesterSettingId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      sessionId,
      title,
      accronym,
      position,
      startDate,
      endDate,
      lateRegistrationStartDate,
      statusText,
      semesterSettingId);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SemestersTableData &&
          other.id == this.id &&
          other.sessionId == this.sessionId &&
          other.title == this.title &&
          other.accronym == this.accronym &&
          other.position == this.position &&
          other.startDate == this.startDate &&
          other.endDate == this.endDate &&
          other.lateRegistrationStartDate == this.lateRegistrationStartDate &&
          other.statusText == this.statusText &&
          other.semesterSettingId == this.semesterSettingId);
}

class SemestersTableCompanion extends UpdateCompanion<SemestersTableData> {
  final Value<int> id;
  final Value<int> sessionId;
  final Value<String?> title;
  final Value<String?> accronym;
  final Value<int?> position;
  final Value<String?> startDate;
  final Value<String?> endDate;
  final Value<String?> lateRegistrationStartDate;
  final Value<String?> statusText;
  final Value<int?> semesterSettingId;
  const SemestersTableCompanion({
    this.id = const Value.absent(),
    this.sessionId = const Value.absent(),
    this.title = const Value.absent(),
    this.accronym = const Value.absent(),
    this.position = const Value.absent(),
    this.startDate = const Value.absent(),
    this.endDate = const Value.absent(),
    this.lateRegistrationStartDate = const Value.absent(),
    this.statusText = const Value.absent(),
    this.semesterSettingId = const Value.absent(),
  });
  SemestersTableCompanion.insert({
    this.id = const Value.absent(),
    required int sessionId,
    this.title = const Value.absent(),
    this.accronym = const Value.absent(),
    this.position = const Value.absent(),
    this.startDate = const Value.absent(),
    this.endDate = const Value.absent(),
    this.lateRegistrationStartDate = const Value.absent(),
    this.statusText = const Value.absent(),
    this.semesterSettingId = const Value.absent(),
  }) : sessionId = Value(sessionId);
  static Insertable<SemestersTableData> custom({
    Expression<int>? id,
    Expression<int>? sessionId,
    Expression<String>? title,
    Expression<String>? accronym,
    Expression<int>? position,
    Expression<String>? startDate,
    Expression<String>? endDate,
    Expression<String>? lateRegistrationStartDate,
    Expression<String>? statusText,
    Expression<int>? semesterSettingId,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (sessionId != null) 'session_id': sessionId,
      if (title != null) 'title': title,
      if (accronym != null) 'accronym': accronym,
      if (position != null) 'position': position,
      if (startDate != null) 'start_date': startDate,
      if (endDate != null) 'end_date': endDate,
      if (lateRegistrationStartDate != null)
        'late_registration_start_date': lateRegistrationStartDate,
      if (statusText != null) 'status_text': statusText,
      if (semesterSettingId != null) 'semester_setting_id': semesterSettingId,
    });
  }

  SemestersTableCompanion copyWith(
      {Value<int>? id,
      Value<int>? sessionId,
      Value<String?>? title,
      Value<String?>? accronym,
      Value<int?>? position,
      Value<String?>? startDate,
      Value<String?>? endDate,
      Value<String?>? lateRegistrationStartDate,
      Value<String?>? statusText,
      Value<int?>? semesterSettingId}) {
    return SemestersTableCompanion(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      title: title ?? this.title,
      accronym: accronym ?? this.accronym,
      position: position ?? this.position,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      lateRegistrationStartDate:
          lateRegistrationStartDate ?? this.lateRegistrationStartDate,
      statusText: statusText ?? this.statusText,
      semesterSettingId: semesterSettingId ?? this.semesterSettingId,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (sessionId.present) {
      map['session_id'] = Variable<int>(sessionId.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (accronym.present) {
      map['accronym'] = Variable<String>(accronym.value);
    }
    if (position.present) {
      map['position'] = Variable<int>(position.value);
    }
    if (startDate.present) {
      map['start_date'] = Variable<String>(startDate.value);
    }
    if (endDate.present) {
      map['end_date'] = Variable<String>(endDate.value);
    }
    if (lateRegistrationStartDate.present) {
      map['late_registration_start_date'] =
          Variable<String>(lateRegistrationStartDate.value);
    }
    if (statusText.present) {
      map['status_text'] = Variable<String>(statusText.value);
    }
    if (semesterSettingId.present) {
      map['semester_setting_id'] = Variable<int>(semesterSettingId.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SemestersTableCompanion(')
          ..write('id: $id, ')
          ..write('sessionId: $sessionId, ')
          ..write('title: $title, ')
          ..write('accronym: $accronym, ')
          ..write('position: $position, ')
          ..write('startDate: $startDate, ')
          ..write('endDate: $endDate, ')
          ..write('lateRegistrationStartDate: $lateRegistrationStartDate, ')
          ..write('statusText: $statusText, ')
          ..write('semesterSettingId: $semesterSettingId')
          ..write(')'))
        .toString();
  }
}

class $CoursesTableTable extends CoursesTable
    with TableInfo<$CoursesTableTable, CoursesTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CoursesTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _courseCodeMeta =
      const VerificationMeta('courseCode');
  @override
  late final GeneratedColumn<String> courseCode = GeneratedColumn<String>(
      'course_code', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _courseTitleMeta =
      const VerificationMeta('courseTitle');
  @override
  late final GeneratedColumn<String> courseTitle = GeneratedColumn<String>(
      'course_title', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _creditUnitMeta =
      const VerificationMeta('creditUnit');
  @override
  late final GeneratedColumn<int> creditUnit = GeneratedColumn<int>(
      'credit_unit', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _courseSynopsisMeta =
      const VerificationMeta('courseSynopsis');
  @override
  late final GeneratedColumn<String> courseSynopsis = GeneratedColumn<String>(
      'course_synopsis', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _hostIdMeta = const VerificationMeta('hostId');
  @override
  late final GeneratedColumn<int> hostId = GeneratedColumn<int>(
      'host_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _departmentIdMeta =
      const VerificationMeta('departmentId');
  @override
  late final GeneratedColumn<int> departmentId = GeneratedColumn<int>(
      'department_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _passMarkMeta =
      const VerificationMeta('passMark');
  @override
  late final GeneratedColumn<int> passMark = GeneratedColumn<int>(
      'pass_mark', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _lecturersMeta =
      const VerificationMeta('lecturers');
  @override
  late final GeneratedColumn<String> lecturers = GeneratedColumn<String>(
      'lecturers', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        courseCode,
        courseTitle,
        creditUnit,
        courseSynopsis,
        type,
        hostId,
        departmentId,
        passMark,
        lecturers
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'courses_table';
  @override
  VerificationContext validateIntegrity(Insertable<CoursesTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('course_code')) {
      context.handle(
          _courseCodeMeta,
          courseCode.isAcceptableOrUnknown(
              data['course_code']!, _courseCodeMeta));
    } else if (isInserting) {
      context.missing(_courseCodeMeta);
    }
    if (data.containsKey('course_title')) {
      context.handle(
          _courseTitleMeta,
          courseTitle.isAcceptableOrUnknown(
              data['course_title']!, _courseTitleMeta));
    } else if (isInserting) {
      context.missing(_courseTitleMeta);
    }
    if (data.containsKey('credit_unit')) {
      context.handle(
          _creditUnitMeta,
          creditUnit.isAcceptableOrUnknown(
              data['credit_unit']!, _creditUnitMeta));
    } else if (isInserting) {
      context.missing(_creditUnitMeta);
    }
    if (data.containsKey('course_synopsis')) {
      context.handle(
          _courseSynopsisMeta,
          courseSynopsis.isAcceptableOrUnknown(
              data['course_synopsis']!, _courseSynopsisMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('host_id')) {
      context.handle(_hostIdMeta,
          hostId.isAcceptableOrUnknown(data['host_id']!, _hostIdMeta));
    }
    if (data.containsKey('department_id')) {
      context.handle(
          _departmentIdMeta,
          departmentId.isAcceptableOrUnknown(
              data['department_id']!, _departmentIdMeta));
    }
    if (data.containsKey('pass_mark')) {
      context.handle(_passMarkMeta,
          passMark.isAcceptableOrUnknown(data['pass_mark']!, _passMarkMeta));
    }
    if (data.containsKey('lecturers')) {
      context.handle(_lecturersMeta,
          lecturers.isAcceptableOrUnknown(data['lecturers']!, _lecturersMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CoursesTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CoursesTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      courseCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}course_code'])!,
      courseTitle: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}course_title'])!,
      creditUnit: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}credit_unit'])!,
      courseSynopsis: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}course_synopsis']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      hostId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}host_id']),
      departmentId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}department_id']),
      passMark: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}pass_mark']),
      lecturers: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}lecturers']),
    );
  }

  @override
  $CoursesTableTable createAlias(String alias) {
    return $CoursesTableTable(attachedDatabase, alias);
  }
}

class CoursesTableData extends DataClass
    implements Insertable<CoursesTableData> {
  final int id;
  final String courseCode;
  final String courseTitle;
  final int creditUnit;
  final String? courseSynopsis;
  final String type;
  final int? hostId;
  final int? departmentId;
  final int? passMark;
  final String? lecturers;
  const CoursesTableData(
      {required this.id,
      required this.courseCode,
      required this.courseTitle,
      required this.creditUnit,
      this.courseSynopsis,
      required this.type,
      this.hostId,
      this.departmentId,
      this.passMark,
      this.lecturers});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['course_code'] = Variable<String>(courseCode);
    map['course_title'] = Variable<String>(courseTitle);
    map['credit_unit'] = Variable<int>(creditUnit);
    if (!nullToAbsent || courseSynopsis != null) {
      map['course_synopsis'] = Variable<String>(courseSynopsis);
    }
    map['type'] = Variable<String>(type);
    if (!nullToAbsent || hostId != null) {
      map['host_id'] = Variable<int>(hostId);
    }
    if (!nullToAbsent || departmentId != null) {
      map['department_id'] = Variable<int>(departmentId);
    }
    if (!nullToAbsent || passMark != null) {
      map['pass_mark'] = Variable<int>(passMark);
    }
    if (!nullToAbsent || lecturers != null) {
      map['lecturers'] = Variable<String>(lecturers);
    }
    return map;
  }

  CoursesTableCompanion toCompanion(bool nullToAbsent) {
    return CoursesTableCompanion(
      id: Value(id),
      courseCode: Value(courseCode),
      courseTitle: Value(courseTitle),
      creditUnit: Value(creditUnit),
      courseSynopsis: courseSynopsis == null && nullToAbsent
          ? const Value.absent()
          : Value(courseSynopsis),
      type: Value(type),
      hostId:
          hostId == null && nullToAbsent ? const Value.absent() : Value(hostId),
      departmentId: departmentId == null && nullToAbsent
          ? const Value.absent()
          : Value(departmentId),
      passMark: passMark == null && nullToAbsent
          ? const Value.absent()
          : Value(passMark),
      lecturers: lecturers == null && nullToAbsent
          ? const Value.absent()
          : Value(lecturers),
    );
  }

  factory CoursesTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CoursesTableData(
      id: serializer.fromJson<int>(json['id']),
      courseCode: serializer.fromJson<String>(json['courseCode']),
      courseTitle: serializer.fromJson<String>(json['courseTitle']),
      creditUnit: serializer.fromJson<int>(json['creditUnit']),
      courseSynopsis: serializer.fromJson<String?>(json['courseSynopsis']),
      type: serializer.fromJson<String>(json['type']),
      hostId: serializer.fromJson<int?>(json['hostId']),
      departmentId: serializer.fromJson<int?>(json['departmentId']),
      passMark: serializer.fromJson<int?>(json['passMark']),
      lecturers: serializer.fromJson<String?>(json['lecturers']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'courseCode': serializer.toJson<String>(courseCode),
      'courseTitle': serializer.toJson<String>(courseTitle),
      'creditUnit': serializer.toJson<int>(creditUnit),
      'courseSynopsis': serializer.toJson<String?>(courseSynopsis),
      'type': serializer.toJson<String>(type),
      'hostId': serializer.toJson<int?>(hostId),
      'departmentId': serializer.toJson<int?>(departmentId),
      'passMark': serializer.toJson<int?>(passMark),
      'lecturers': serializer.toJson<String?>(lecturers),
    };
  }

  CoursesTableData copyWith(
          {int? id,
          String? courseCode,
          String? courseTitle,
          int? creditUnit,
          Value<String?> courseSynopsis = const Value.absent(),
          String? type,
          Value<int?> hostId = const Value.absent(),
          Value<int?> departmentId = const Value.absent(),
          Value<int?> passMark = const Value.absent(),
          Value<String?> lecturers = const Value.absent()}) =>
      CoursesTableData(
        id: id ?? this.id,
        courseCode: courseCode ?? this.courseCode,
        courseTitle: courseTitle ?? this.courseTitle,
        creditUnit: creditUnit ?? this.creditUnit,
        courseSynopsis:
            courseSynopsis.present ? courseSynopsis.value : this.courseSynopsis,
        type: type ?? this.type,
        hostId: hostId.present ? hostId.value : this.hostId,
        departmentId:
            departmentId.present ? departmentId.value : this.departmentId,
        passMark: passMark.present ? passMark.value : this.passMark,
        lecturers: lecturers.present ? lecturers.value : this.lecturers,
      );
  CoursesTableData copyWithCompanion(CoursesTableCompanion data) {
    return CoursesTableData(
      id: data.id.present ? data.id.value : this.id,
      courseCode:
          data.courseCode.present ? data.courseCode.value : this.courseCode,
      courseTitle:
          data.courseTitle.present ? data.courseTitle.value : this.courseTitle,
      creditUnit:
          data.creditUnit.present ? data.creditUnit.value : this.creditUnit,
      courseSynopsis: data.courseSynopsis.present
          ? data.courseSynopsis.value
          : this.courseSynopsis,
      type: data.type.present ? data.type.value : this.type,
      hostId: data.hostId.present ? data.hostId.value : this.hostId,
      departmentId: data.departmentId.present
          ? data.departmentId.value
          : this.departmentId,
      passMark: data.passMark.present ? data.passMark.value : this.passMark,
      lecturers: data.lecturers.present ? data.lecturers.value : this.lecturers,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CoursesTableData(')
          ..write('id: $id, ')
          ..write('courseCode: $courseCode, ')
          ..write('courseTitle: $courseTitle, ')
          ..write('creditUnit: $creditUnit, ')
          ..write('courseSynopsis: $courseSynopsis, ')
          ..write('type: $type, ')
          ..write('hostId: $hostId, ')
          ..write('departmentId: $departmentId, ')
          ..write('passMark: $passMark, ')
          ..write('lecturers: $lecturers')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, courseCode, courseTitle, creditUnit,
      courseSynopsis, type, hostId, departmentId, passMark, lecturers);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CoursesTableData &&
          other.id == this.id &&
          other.courseCode == this.courseCode &&
          other.courseTitle == this.courseTitle &&
          other.creditUnit == this.creditUnit &&
          other.courseSynopsis == this.courseSynopsis &&
          other.type == this.type &&
          other.hostId == this.hostId &&
          other.departmentId == this.departmentId &&
          other.passMark == this.passMark &&
          other.lecturers == this.lecturers);
}

class CoursesTableCompanion extends UpdateCompanion<CoursesTableData> {
  final Value<int> id;
  final Value<String> courseCode;
  final Value<String> courseTitle;
  final Value<int> creditUnit;
  final Value<String?> courseSynopsis;
  final Value<String> type;
  final Value<int?> hostId;
  final Value<int?> departmentId;
  final Value<int?> passMark;
  final Value<String?> lecturers;
  const CoursesTableCompanion({
    this.id = const Value.absent(),
    this.courseCode = const Value.absent(),
    this.courseTitle = const Value.absent(),
    this.creditUnit = const Value.absent(),
    this.courseSynopsis = const Value.absent(),
    this.type = const Value.absent(),
    this.hostId = const Value.absent(),
    this.departmentId = const Value.absent(),
    this.passMark = const Value.absent(),
    this.lecturers = const Value.absent(),
  });
  CoursesTableCompanion.insert({
    this.id = const Value.absent(),
    required String courseCode,
    required String courseTitle,
    required int creditUnit,
    this.courseSynopsis = const Value.absent(),
    required String type,
    this.hostId = const Value.absent(),
    this.departmentId = const Value.absent(),
    this.passMark = const Value.absent(),
    this.lecturers = const Value.absent(),
  })  : courseCode = Value(courseCode),
        courseTitle = Value(courseTitle),
        creditUnit = Value(creditUnit),
        type = Value(type);
  static Insertable<CoursesTableData> custom({
    Expression<int>? id,
    Expression<String>? courseCode,
    Expression<String>? courseTitle,
    Expression<int>? creditUnit,
    Expression<String>? courseSynopsis,
    Expression<String>? type,
    Expression<int>? hostId,
    Expression<int>? departmentId,
    Expression<int>? passMark,
    Expression<String>? lecturers,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (courseCode != null) 'course_code': courseCode,
      if (courseTitle != null) 'course_title': courseTitle,
      if (creditUnit != null) 'credit_unit': creditUnit,
      if (courseSynopsis != null) 'course_synopsis': courseSynopsis,
      if (type != null) 'type': type,
      if (hostId != null) 'host_id': hostId,
      if (departmentId != null) 'department_id': departmentId,
      if (passMark != null) 'pass_mark': passMark,
      if (lecturers != null) 'lecturers': lecturers,
    });
  }

  CoursesTableCompanion copyWith(
      {Value<int>? id,
      Value<String>? courseCode,
      Value<String>? courseTitle,
      Value<int>? creditUnit,
      Value<String?>? courseSynopsis,
      Value<String>? type,
      Value<int?>? hostId,
      Value<int?>? departmentId,
      Value<int?>? passMark,
      Value<String?>? lecturers}) {
    return CoursesTableCompanion(
      id: id ?? this.id,
      courseCode: courseCode ?? this.courseCode,
      courseTitle: courseTitle ?? this.courseTitle,
      creditUnit: creditUnit ?? this.creditUnit,
      courseSynopsis: courseSynopsis ?? this.courseSynopsis,
      type: type ?? this.type,
      hostId: hostId ?? this.hostId,
      departmentId: departmentId ?? this.departmentId,
      passMark: passMark ?? this.passMark,
      lecturers: lecturers ?? this.lecturers,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (courseCode.present) {
      map['course_code'] = Variable<String>(courseCode.value);
    }
    if (courseTitle.present) {
      map['course_title'] = Variable<String>(courseTitle.value);
    }
    if (creditUnit.present) {
      map['credit_unit'] = Variable<int>(creditUnit.value);
    }
    if (courseSynopsis.present) {
      map['course_synopsis'] = Variable<String>(courseSynopsis.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (hostId.present) {
      map['host_id'] = Variable<int>(hostId.value);
    }
    if (departmentId.present) {
      map['department_id'] = Variable<int>(departmentId.value);
    }
    if (passMark.present) {
      map['pass_mark'] = Variable<int>(passMark.value);
    }
    if (lecturers.present) {
      map['lecturers'] = Variable<String>(lecturers.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CoursesTableCompanion(')
          ..write('id: $id, ')
          ..write('courseCode: $courseCode, ')
          ..write('courseTitle: $courseTitle, ')
          ..write('creditUnit: $creditUnit, ')
          ..write('courseSynopsis: $courseSynopsis, ')
          ..write('type: $type, ')
          ..write('hostId: $hostId, ')
          ..write('departmentId: $departmentId, ')
          ..write('passMark: $passMark, ')
          ..write('lecturers: $lecturers')
          ..write(')'))
        .toString();
  }
}

class $CourseRegistrationsTableTable extends CourseRegistrationsTable
    with
        TableInfo<$CourseRegistrationsTableTable,
            CourseRegistrationsTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CourseRegistrationsTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _courseIdMeta =
      const VerificationMeta('courseId');
  @override
  late final GeneratedColumn<int> courseId = GeneratedColumn<int>(
      'course_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _sessionIdMeta =
      const VerificationMeta('sessionId');
  @override
  late final GeneratedColumn<int> sessionId = GeneratedColumn<int>(
      'session_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _semesterAccronymMeta =
      const VerificationMeta('semesterAccronym');
  @override
  late final GeneratedColumn<String> semesterAccronym = GeneratedColumn<String>(
      'semester_accronym', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _semesterPositionMeta =
      const VerificationMeta('semesterPosition');
  @override
  late final GeneratedColumn<int> semesterPosition = GeneratedColumn<int>(
      'semester_position', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _semesterTitleMeta =
      const VerificationMeta('semesterTitle');
  @override
  late final GeneratedColumn<String> semesterTitle = GeneratedColumn<String>(
      'semester_title', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _scoreMeta = const VerificationMeta('score');
  @override
  late final GeneratedColumn<String> score = GeneratedColumn<String>(
      'score', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _courseStatusMeta =
      const VerificationMeta('courseStatus');
  @override
  late final GeneratedColumn<String> courseStatus = GeneratedColumn<String>(
      'course_status', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _approvalStatusMeta =
      const VerificationMeta('approvalStatus');
  @override
  late final GeneratedColumn<String> approvalStatus = GeneratedColumn<String>(
      'approval_status', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _approvalRemarkMeta =
      const VerificationMeta('approvalRemark');
  @override
  late final GeneratedColumn<String> approvalRemark = GeneratedColumn<String>(
      'approval_remark', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _remarkMeta = const VerificationMeta('remark');
  @override
  late final GeneratedColumn<String> remark = GeneratedColumn<String>(
      'remark', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<String> createdAt = GeneratedColumn<String>(
      'created_at', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<String> updatedAt = GeneratedColumn<String>(
      'updated_at', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        courseId,
        sessionId,
        semesterAccronym,
        semesterPosition,
        semesterTitle,
        score,
        courseStatus,
        approvalStatus,
        approvalRemark,
        remark,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'course_registrations_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CourseRegistrationsTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('course_id')) {
      context.handle(_courseIdMeta,
          courseId.isAcceptableOrUnknown(data['course_id']!, _courseIdMeta));
    } else if (isInserting) {
      context.missing(_courseIdMeta);
    }
    if (data.containsKey('session_id')) {
      context.handle(_sessionIdMeta,
          sessionId.isAcceptableOrUnknown(data['session_id']!, _sessionIdMeta));
    }
    if (data.containsKey('semester_accronym')) {
      context.handle(
          _semesterAccronymMeta,
          semesterAccronym.isAcceptableOrUnknown(
              data['semester_accronym']!, _semesterAccronymMeta));
    }
    if (data.containsKey('semester_position')) {
      context.handle(
          _semesterPositionMeta,
          semesterPosition.isAcceptableOrUnknown(
              data['semester_position']!, _semesterPositionMeta));
    }
    if (data.containsKey('semester_title')) {
      context.handle(
          _semesterTitleMeta,
          semesterTitle.isAcceptableOrUnknown(
              data['semester_title']!, _semesterTitleMeta));
    }
    if (data.containsKey('score')) {
      context.handle(
          _scoreMeta, score.isAcceptableOrUnknown(data['score']!, _scoreMeta));
    }
    if (data.containsKey('course_status')) {
      context.handle(
          _courseStatusMeta,
          courseStatus.isAcceptableOrUnknown(
              data['course_status']!, _courseStatusMeta));
    }
    if (data.containsKey('approval_status')) {
      context.handle(
          _approvalStatusMeta,
          approvalStatus.isAcceptableOrUnknown(
              data['approval_status']!, _approvalStatusMeta));
    }
    if (data.containsKey('approval_remark')) {
      context.handle(
          _approvalRemarkMeta,
          approvalRemark.isAcceptableOrUnknown(
              data['approval_remark']!, _approvalRemarkMeta));
    }
    if (data.containsKey('remark')) {
      context.handle(_remarkMeta,
          remark.isAcceptableOrUnknown(data['remark']!, _remarkMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CourseRegistrationsTableData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CourseRegistrationsTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      courseId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}course_id'])!,
      sessionId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}session_id']),
      semesterAccronym: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}semester_accronym']),
      semesterPosition: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}semester_position']),
      semesterTitle: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}semester_title']),
      score: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}score']),
      courseStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}course_status']),
      approvalStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}approval_status']),
      approvalRemark: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}approval_remark']),
      remark: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}remark']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}created_at']),
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}updated_at']),
    );
  }

  @override
  $CourseRegistrationsTableTable createAlias(String alias) {
    return $CourseRegistrationsTableTable(attachedDatabase, alias);
  }
}

class CourseRegistrationsTableData extends DataClass
    implements Insertable<CourseRegistrationsTableData> {
  final int id;
  final int courseId;
  final int? sessionId;
  final String? semesterAccronym;
  final int? semesterPosition;
  final String? semesterTitle;
  final String? score;
  final String? courseStatus;
  final String? approvalStatus;
  final String? approvalRemark;
  final String? remark;
  final String? createdAt;
  final String? updatedAt;
  const CourseRegistrationsTableData(
      {required this.id,
      required this.courseId,
      this.sessionId,
      this.semesterAccronym,
      this.semesterPosition,
      this.semesterTitle,
      this.score,
      this.courseStatus,
      this.approvalStatus,
      this.approvalRemark,
      this.remark,
      this.createdAt,
      this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['course_id'] = Variable<int>(courseId);
    if (!nullToAbsent || sessionId != null) {
      map['session_id'] = Variable<int>(sessionId);
    }
    if (!nullToAbsent || semesterAccronym != null) {
      map['semester_accronym'] = Variable<String>(semesterAccronym);
    }
    if (!nullToAbsent || semesterPosition != null) {
      map['semester_position'] = Variable<int>(semesterPosition);
    }
    if (!nullToAbsent || semesterTitle != null) {
      map['semester_title'] = Variable<String>(semesterTitle);
    }
    if (!nullToAbsent || score != null) {
      map['score'] = Variable<String>(score);
    }
    if (!nullToAbsent || courseStatus != null) {
      map['course_status'] = Variable<String>(courseStatus);
    }
    if (!nullToAbsent || approvalStatus != null) {
      map['approval_status'] = Variable<String>(approvalStatus);
    }
    if (!nullToAbsent || approvalRemark != null) {
      map['approval_remark'] = Variable<String>(approvalRemark);
    }
    if (!nullToAbsent || remark != null) {
      map['remark'] = Variable<String>(remark);
    }
    if (!nullToAbsent || createdAt != null) {
      map['created_at'] = Variable<String>(createdAt);
    }
    if (!nullToAbsent || updatedAt != null) {
      map['updated_at'] = Variable<String>(updatedAt);
    }
    return map;
  }

  CourseRegistrationsTableCompanion toCompanion(bool nullToAbsent) {
    return CourseRegistrationsTableCompanion(
      id: Value(id),
      courseId: Value(courseId),
      sessionId: sessionId == null && nullToAbsent
          ? const Value.absent()
          : Value(sessionId),
      semesterAccronym: semesterAccronym == null && nullToAbsent
          ? const Value.absent()
          : Value(semesterAccronym),
      semesterPosition: semesterPosition == null && nullToAbsent
          ? const Value.absent()
          : Value(semesterPosition),
      semesterTitle: semesterTitle == null && nullToAbsent
          ? const Value.absent()
          : Value(semesterTitle),
      score:
          score == null && nullToAbsent ? const Value.absent() : Value(score),
      courseStatus: courseStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(courseStatus),
      approvalStatus: approvalStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(approvalStatus),
      approvalRemark: approvalRemark == null && nullToAbsent
          ? const Value.absent()
          : Value(approvalRemark),
      remark:
          remark == null && nullToAbsent ? const Value.absent() : Value(remark),
      createdAt: createdAt == null && nullToAbsent
          ? const Value.absent()
          : Value(createdAt),
      updatedAt: updatedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(updatedAt),
    );
  }

  factory CourseRegistrationsTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CourseRegistrationsTableData(
      id: serializer.fromJson<int>(json['id']),
      courseId: serializer.fromJson<int>(json['courseId']),
      sessionId: serializer.fromJson<int?>(json['sessionId']),
      semesterAccronym: serializer.fromJson<String?>(json['semesterAccronym']),
      semesterPosition: serializer.fromJson<int?>(json['semesterPosition']),
      semesterTitle: serializer.fromJson<String?>(json['semesterTitle']),
      score: serializer.fromJson<String?>(json['score']),
      courseStatus: serializer.fromJson<String?>(json['courseStatus']),
      approvalStatus: serializer.fromJson<String?>(json['approvalStatus']),
      approvalRemark: serializer.fromJson<String?>(json['approvalRemark']),
      remark: serializer.fromJson<String?>(json['remark']),
      createdAt: serializer.fromJson<String?>(json['createdAt']),
      updatedAt: serializer.fromJson<String?>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'courseId': serializer.toJson<int>(courseId),
      'sessionId': serializer.toJson<int?>(sessionId),
      'semesterAccronym': serializer.toJson<String?>(semesterAccronym),
      'semesterPosition': serializer.toJson<int?>(semesterPosition),
      'semesterTitle': serializer.toJson<String?>(semesterTitle),
      'score': serializer.toJson<String?>(score),
      'courseStatus': serializer.toJson<String?>(courseStatus),
      'approvalStatus': serializer.toJson<String?>(approvalStatus),
      'approvalRemark': serializer.toJson<String?>(approvalRemark),
      'remark': serializer.toJson<String?>(remark),
      'createdAt': serializer.toJson<String?>(createdAt),
      'updatedAt': serializer.toJson<String?>(updatedAt),
    };
  }

  CourseRegistrationsTableData copyWith(
          {int? id,
          int? courseId,
          Value<int?> sessionId = const Value.absent(),
          Value<String?> semesterAccronym = const Value.absent(),
          Value<int?> semesterPosition = const Value.absent(),
          Value<String?> semesterTitle = const Value.absent(),
          Value<String?> score = const Value.absent(),
          Value<String?> courseStatus = const Value.absent(),
          Value<String?> approvalStatus = const Value.absent(),
          Value<String?> approvalRemark = const Value.absent(),
          Value<String?> remark = const Value.absent(),
          Value<String?> createdAt = const Value.absent(),
          Value<String?> updatedAt = const Value.absent()}) =>
      CourseRegistrationsTableData(
        id: id ?? this.id,
        courseId: courseId ?? this.courseId,
        sessionId: sessionId.present ? sessionId.value : this.sessionId,
        semesterAccronym: semesterAccronym.present
            ? semesterAccronym.value
            : this.semesterAccronym,
        semesterPosition: semesterPosition.present
            ? semesterPosition.value
            : this.semesterPosition,
        semesterTitle:
            semesterTitle.present ? semesterTitle.value : this.semesterTitle,
        score: score.present ? score.value : this.score,
        courseStatus:
            courseStatus.present ? courseStatus.value : this.courseStatus,
        approvalStatus:
            approvalStatus.present ? approvalStatus.value : this.approvalStatus,
        approvalRemark:
            approvalRemark.present ? approvalRemark.value : this.approvalRemark,
        remark: remark.present ? remark.value : this.remark,
        createdAt: createdAt.present ? createdAt.value : this.createdAt,
        updatedAt: updatedAt.present ? updatedAt.value : this.updatedAt,
      );
  CourseRegistrationsTableData copyWithCompanion(
      CourseRegistrationsTableCompanion data) {
    return CourseRegistrationsTableData(
      id: data.id.present ? data.id.value : this.id,
      courseId: data.courseId.present ? data.courseId.value : this.courseId,
      sessionId: data.sessionId.present ? data.sessionId.value : this.sessionId,
      semesterAccronym: data.semesterAccronym.present
          ? data.semesterAccronym.value
          : this.semesterAccronym,
      semesterPosition: data.semesterPosition.present
          ? data.semesterPosition.value
          : this.semesterPosition,
      semesterTitle: data.semesterTitle.present
          ? data.semesterTitle.value
          : this.semesterTitle,
      score: data.score.present ? data.score.value : this.score,
      courseStatus: data.courseStatus.present
          ? data.courseStatus.value
          : this.courseStatus,
      approvalStatus: data.approvalStatus.present
          ? data.approvalStatus.value
          : this.approvalStatus,
      approvalRemark: data.approvalRemark.present
          ? data.approvalRemark.value
          : this.approvalRemark,
      remark: data.remark.present ? data.remark.value : this.remark,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CourseRegistrationsTableData(')
          ..write('id: $id, ')
          ..write('courseId: $courseId, ')
          ..write('sessionId: $sessionId, ')
          ..write('semesterAccronym: $semesterAccronym, ')
          ..write('semesterPosition: $semesterPosition, ')
          ..write('semesterTitle: $semesterTitle, ')
          ..write('score: $score, ')
          ..write('courseStatus: $courseStatus, ')
          ..write('approvalStatus: $approvalStatus, ')
          ..write('approvalRemark: $approvalRemark, ')
          ..write('remark: $remark, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      courseId,
      sessionId,
      semesterAccronym,
      semesterPosition,
      semesterTitle,
      score,
      courseStatus,
      approvalStatus,
      approvalRemark,
      remark,
      createdAt,
      updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CourseRegistrationsTableData &&
          other.id == this.id &&
          other.courseId == this.courseId &&
          other.sessionId == this.sessionId &&
          other.semesterAccronym == this.semesterAccronym &&
          other.semesterPosition == this.semesterPosition &&
          other.semesterTitle == this.semesterTitle &&
          other.score == this.score &&
          other.courseStatus == this.courseStatus &&
          other.approvalStatus == this.approvalStatus &&
          other.approvalRemark == this.approvalRemark &&
          other.remark == this.remark &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class CourseRegistrationsTableCompanion
    extends UpdateCompanion<CourseRegistrationsTableData> {
  final Value<int> id;
  final Value<int> courseId;
  final Value<int?> sessionId;
  final Value<String?> semesterAccronym;
  final Value<int?> semesterPosition;
  final Value<String?> semesterTitle;
  final Value<String?> score;
  final Value<String?> courseStatus;
  final Value<String?> approvalStatus;
  final Value<String?> approvalRemark;
  final Value<String?> remark;
  final Value<String?> createdAt;
  final Value<String?> updatedAt;
  const CourseRegistrationsTableCompanion({
    this.id = const Value.absent(),
    this.courseId = const Value.absent(),
    this.sessionId = const Value.absent(),
    this.semesterAccronym = const Value.absent(),
    this.semesterPosition = const Value.absent(),
    this.semesterTitle = const Value.absent(),
    this.score = const Value.absent(),
    this.courseStatus = const Value.absent(),
    this.approvalStatus = const Value.absent(),
    this.approvalRemark = const Value.absent(),
    this.remark = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  CourseRegistrationsTableCompanion.insert({
    this.id = const Value.absent(),
    required int courseId,
    this.sessionId = const Value.absent(),
    this.semesterAccronym = const Value.absent(),
    this.semesterPosition = const Value.absent(),
    this.semesterTitle = const Value.absent(),
    this.score = const Value.absent(),
    this.courseStatus = const Value.absent(),
    this.approvalStatus = const Value.absent(),
    this.approvalRemark = const Value.absent(),
    this.remark = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  }) : courseId = Value(courseId);
  static Insertable<CourseRegistrationsTableData> custom({
    Expression<int>? id,
    Expression<int>? courseId,
    Expression<int>? sessionId,
    Expression<String>? semesterAccronym,
    Expression<int>? semesterPosition,
    Expression<String>? semesterTitle,
    Expression<String>? score,
    Expression<String>? courseStatus,
    Expression<String>? approvalStatus,
    Expression<String>? approvalRemark,
    Expression<String>? remark,
    Expression<String>? createdAt,
    Expression<String>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (courseId != null) 'course_id': courseId,
      if (sessionId != null) 'session_id': sessionId,
      if (semesterAccronym != null) 'semester_accronym': semesterAccronym,
      if (semesterPosition != null) 'semester_position': semesterPosition,
      if (semesterTitle != null) 'semester_title': semesterTitle,
      if (score != null) 'score': score,
      if (courseStatus != null) 'course_status': courseStatus,
      if (approvalStatus != null) 'approval_status': approvalStatus,
      if (approvalRemark != null) 'approval_remark': approvalRemark,
      if (remark != null) 'remark': remark,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  CourseRegistrationsTableCompanion copyWith(
      {Value<int>? id,
      Value<int>? courseId,
      Value<int?>? sessionId,
      Value<String?>? semesterAccronym,
      Value<int?>? semesterPosition,
      Value<String?>? semesterTitle,
      Value<String?>? score,
      Value<String?>? courseStatus,
      Value<String?>? approvalStatus,
      Value<String?>? approvalRemark,
      Value<String?>? remark,
      Value<String?>? createdAt,
      Value<String?>? updatedAt}) {
    return CourseRegistrationsTableCompanion(
      id: id ?? this.id,
      courseId: courseId ?? this.courseId,
      sessionId: sessionId ?? this.sessionId,
      semesterAccronym: semesterAccronym ?? this.semesterAccronym,
      semesterPosition: semesterPosition ?? this.semesterPosition,
      semesterTitle: semesterTitle ?? this.semesterTitle,
      score: score ?? this.score,
      courseStatus: courseStatus ?? this.courseStatus,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      approvalRemark: approvalRemark ?? this.approvalRemark,
      remark: remark ?? this.remark,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (courseId.present) {
      map['course_id'] = Variable<int>(courseId.value);
    }
    if (sessionId.present) {
      map['session_id'] = Variable<int>(sessionId.value);
    }
    if (semesterAccronym.present) {
      map['semester_accronym'] = Variable<String>(semesterAccronym.value);
    }
    if (semesterPosition.present) {
      map['semester_position'] = Variable<int>(semesterPosition.value);
    }
    if (semesterTitle.present) {
      map['semester_title'] = Variable<String>(semesterTitle.value);
    }
    if (score.present) {
      map['score'] = Variable<String>(score.value);
    }
    if (courseStatus.present) {
      map['course_status'] = Variable<String>(courseStatus.value);
    }
    if (approvalStatus.present) {
      map['approval_status'] = Variable<String>(approvalStatus.value);
    }
    if (approvalRemark.present) {
      map['approval_remark'] = Variable<String>(approvalRemark.value);
    }
    if (remark.present) {
      map['remark'] = Variable<String>(remark.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<String>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<String>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CourseRegistrationsTableCompanion(')
          ..write('id: $id, ')
          ..write('courseId: $courseId, ')
          ..write('sessionId: $sessionId, ')
          ..write('semesterAccronym: $semesterAccronym, ')
          ..write('semesterPosition: $semesterPosition, ')
          ..write('semesterTitle: $semesterTitle, ')
          ..write('score: $score, ')
          ..write('courseStatus: $courseStatus, ')
          ..write('approvalStatus: $approvalStatus, ')
          ..write('approvalRemark: $approvalRemark, ')
          ..write('remark: $remark, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

class $CourseSpecificationsTableTable extends CourseSpecificationsTable
    with
        TableInfo<$CourseSpecificationsTableTable,
            CourseSpecificationsTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CourseSpecificationsTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _creditUnitMeta =
      const VerificationMeta('creditUnit');
  @override
  late final GeneratedColumn<int> creditUnit = GeneratedColumn<int>(
      'credit_unit', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _unitsMeta = const VerificationMeta('units');
  @override
  late final GeneratedColumn<String> units = GeneratedColumn<String>(
      'units', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _courseStatusMeta =
      const VerificationMeta('courseStatus');
  @override
  late final GeneratedColumn<String> courseStatus = GeneratedColumn<String>(
      'course_status', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _courseStatusIdMeta =
      const VerificationMeta('courseStatusId');
  @override
  late final GeneratedColumn<String> courseStatusId = GeneratedColumn<String>(
      'course_status_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _semesterAccronymMeta =
      const VerificationMeta('semesterAccronym');
  @override
  late final GeneratedColumn<String> semesterAccronym = GeneratedColumn<String>(
      'semester_accronym', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _semesterTitleMeta =
      const VerificationMeta('semesterTitle');
  @override
  late final GeneratedColumn<String> semesterTitle = GeneratedColumn<String>(
      'semester_title', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<int> status = GeneratedColumn<int>(
      'status', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _passMarkRequiredMeta =
      const VerificationMeta('passMarkRequired');
  @override
  late final GeneratedColumn<int> passMarkRequired = GeneratedColumn<int>(
      'pass_mark_required', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isSiwesMeta =
      const VerificationMeta('isSiwes');
  @override
  late final GeneratedColumn<int> isSiwes = GeneratedColumn<int>(
      'is_siwes', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _hasPreRequisitesMeta =
      const VerificationMeta('hasPreRequisites');
  @override
  late final GeneratedColumn<int> hasPreRequisites = GeneratedColumn<int>(
      'has_pre_requisites', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isUsedBeyondQualifierLevelMeta =
      const VerificationMeta('isUsedBeyondQualifierLevel');
  @override
  late final GeneratedColumn<int> isUsedBeyondQualifierLevel =
      GeneratedColumn<int>('is_used_beyond_qualifier_level', aliasedName, false,
          type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isUsedInResultComputationMeta =
      const VerificationMeta('isUsedInResultComputation');
  @override
  late final GeneratedColumn<int> isUsedInResultComputation =
      GeneratedColumn<int>('is_used_in_result_computation', aliasedName, false,
          type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _sessionIdMeta =
      const VerificationMeta('sessionId');
  @override
  late final GeneratedColumn<int> sessionId = GeneratedColumn<int>(
      'session_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _courseIdMeta =
      const VerificationMeta('courseId');
  @override
  late final GeneratedColumn<int> courseId = GeneratedColumn<int>(
      'course_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        creditUnit,
        units,
        courseStatus,
        courseStatusId,
        semesterAccronym,
        semesterTitle,
        status,
        passMarkRequired,
        isSiwes,
        hasPreRequisites,
        isUsedBeyondQualifierLevel,
        isUsedInResultComputation,
        sessionId,
        courseId
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'course_specifications_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CourseSpecificationsTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('credit_unit')) {
      context.handle(
          _creditUnitMeta,
          creditUnit.isAcceptableOrUnknown(
              data['credit_unit']!, _creditUnitMeta));
    } else if (isInserting) {
      context.missing(_creditUnitMeta);
    }
    if (data.containsKey('units')) {
      context.handle(
          _unitsMeta, units.isAcceptableOrUnknown(data['units']!, _unitsMeta));
    } else if (isInserting) {
      context.missing(_unitsMeta);
    }
    if (data.containsKey('course_status')) {
      context.handle(
          _courseStatusMeta,
          courseStatus.isAcceptableOrUnknown(
              data['course_status']!, _courseStatusMeta));
    } else if (isInserting) {
      context.missing(_courseStatusMeta);
    }
    if (data.containsKey('course_status_id')) {
      context.handle(
          _courseStatusIdMeta,
          courseStatusId.isAcceptableOrUnknown(
              data['course_status_id']!, _courseStatusIdMeta));
    } else if (isInserting) {
      context.missing(_courseStatusIdMeta);
    }
    if (data.containsKey('semester_accronym')) {
      context.handle(
          _semesterAccronymMeta,
          semesterAccronym.isAcceptableOrUnknown(
              data['semester_accronym']!, _semesterAccronymMeta));
    } else if (isInserting) {
      context.missing(_semesterAccronymMeta);
    }
    if (data.containsKey('semester_title')) {
      context.handle(
          _semesterTitleMeta,
          semesterTitle.isAcceptableOrUnknown(
              data['semester_title']!, _semesterTitleMeta));
    } else if (isInserting) {
      context.missing(_semesterTitleMeta);
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    } else if (isInserting) {
      context.missing(_statusMeta);
    }
    if (data.containsKey('pass_mark_required')) {
      context.handle(
          _passMarkRequiredMeta,
          passMarkRequired.isAcceptableOrUnknown(
              data['pass_mark_required']!, _passMarkRequiredMeta));
    } else if (isInserting) {
      context.missing(_passMarkRequiredMeta);
    }
    if (data.containsKey('is_siwes')) {
      context.handle(_isSiwesMeta,
          isSiwes.isAcceptableOrUnknown(data['is_siwes']!, _isSiwesMeta));
    } else if (isInserting) {
      context.missing(_isSiwesMeta);
    }
    if (data.containsKey('has_pre_requisites')) {
      context.handle(
          _hasPreRequisitesMeta,
          hasPreRequisites.isAcceptableOrUnknown(
              data['has_pre_requisites']!, _hasPreRequisitesMeta));
    } else if (isInserting) {
      context.missing(_hasPreRequisitesMeta);
    }
    if (data.containsKey('is_used_beyond_qualifier_level')) {
      context.handle(
          _isUsedBeyondQualifierLevelMeta,
          isUsedBeyondQualifierLevel.isAcceptableOrUnknown(
              data['is_used_beyond_qualifier_level']!,
              _isUsedBeyondQualifierLevelMeta));
    } else if (isInserting) {
      context.missing(_isUsedBeyondQualifierLevelMeta);
    }
    if (data.containsKey('is_used_in_result_computation')) {
      context.handle(
          _isUsedInResultComputationMeta,
          isUsedInResultComputation.isAcceptableOrUnknown(
              data['is_used_in_result_computation']!,
              _isUsedInResultComputationMeta));
    } else if (isInserting) {
      context.missing(_isUsedInResultComputationMeta);
    }
    if (data.containsKey('session_id')) {
      context.handle(_sessionIdMeta,
          sessionId.isAcceptableOrUnknown(data['session_id']!, _sessionIdMeta));
    } else if (isInserting) {
      context.missing(_sessionIdMeta);
    }
    if (data.containsKey('course_id')) {
      context.handle(_courseIdMeta,
          courseId.isAcceptableOrUnknown(data['course_id']!, _courseIdMeta));
    } else if (isInserting) {
      context.missing(_courseIdMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CourseSpecificationsTableData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CourseSpecificationsTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      creditUnit: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}credit_unit'])!,
      units: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}units'])!,
      courseStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}course_status'])!,
      courseStatusId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}course_status_id'])!,
      semesterAccronym: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}semester_accronym'])!,
      semesterTitle: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}semester_title'])!,
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}status'])!,
      passMarkRequired: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}pass_mark_required'])!,
      isSiwes: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}is_siwes'])!,
      hasPreRequisites: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}has_pre_requisites'])!,
      isUsedBeyondQualifierLevel: attachedDatabase.typeMapping.read(
          DriftSqlType.int,
          data['${effectivePrefix}is_used_beyond_qualifier_level'])!,
      isUsedInResultComputation: attachedDatabase.typeMapping.read(
          DriftSqlType.int,
          data['${effectivePrefix}is_used_in_result_computation'])!,
      sessionId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}session_id'])!,
      courseId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}course_id'])!,
    );
  }

  @override
  $CourseSpecificationsTableTable createAlias(String alias) {
    return $CourseSpecificationsTableTable(attachedDatabase, alias);
  }
}

class CourseSpecificationsTableData extends DataClass
    implements Insertable<CourseSpecificationsTableData> {
  final int id;
  final int creditUnit;
  final String units;
  final String courseStatus;
  final String courseStatusId;
  final String semesterAccronym;
  final String semesterTitle;
  final int status;
  final int passMarkRequired;
  final int isSiwes;
  final int hasPreRequisites;
  final int isUsedBeyondQualifierLevel;
  final int isUsedInResultComputation;
  final int sessionId;
  final int courseId;
  const CourseSpecificationsTableData(
      {required this.id,
      required this.creditUnit,
      required this.units,
      required this.courseStatus,
      required this.courseStatusId,
      required this.semesterAccronym,
      required this.semesterTitle,
      required this.status,
      required this.passMarkRequired,
      required this.isSiwes,
      required this.hasPreRequisites,
      required this.isUsedBeyondQualifierLevel,
      required this.isUsedInResultComputation,
      required this.sessionId,
      required this.courseId});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['credit_unit'] = Variable<int>(creditUnit);
    map['units'] = Variable<String>(units);
    map['course_status'] = Variable<String>(courseStatus);
    map['course_status_id'] = Variable<String>(courseStatusId);
    map['semester_accronym'] = Variable<String>(semesterAccronym);
    map['semester_title'] = Variable<String>(semesterTitle);
    map['status'] = Variable<int>(status);
    map['pass_mark_required'] = Variable<int>(passMarkRequired);
    map['is_siwes'] = Variable<int>(isSiwes);
    map['has_pre_requisites'] = Variable<int>(hasPreRequisites);
    map['is_used_beyond_qualifier_level'] =
        Variable<int>(isUsedBeyondQualifierLevel);
    map['is_used_in_result_computation'] =
        Variable<int>(isUsedInResultComputation);
    map['session_id'] = Variable<int>(sessionId);
    map['course_id'] = Variable<int>(courseId);
    return map;
  }

  CourseSpecificationsTableCompanion toCompanion(bool nullToAbsent) {
    return CourseSpecificationsTableCompanion(
      id: Value(id),
      creditUnit: Value(creditUnit),
      units: Value(units),
      courseStatus: Value(courseStatus),
      courseStatusId: Value(courseStatusId),
      semesterAccronym: Value(semesterAccronym),
      semesterTitle: Value(semesterTitle),
      status: Value(status),
      passMarkRequired: Value(passMarkRequired),
      isSiwes: Value(isSiwes),
      hasPreRequisites: Value(hasPreRequisites),
      isUsedBeyondQualifierLevel: Value(isUsedBeyondQualifierLevel),
      isUsedInResultComputation: Value(isUsedInResultComputation),
      sessionId: Value(sessionId),
      courseId: Value(courseId),
    );
  }

  factory CourseSpecificationsTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CourseSpecificationsTableData(
      id: serializer.fromJson<int>(json['id']),
      creditUnit: serializer.fromJson<int>(json['creditUnit']),
      units: serializer.fromJson<String>(json['units']),
      courseStatus: serializer.fromJson<String>(json['courseStatus']),
      courseStatusId: serializer.fromJson<String>(json['courseStatusId']),
      semesterAccronym: serializer.fromJson<String>(json['semesterAccronym']),
      semesterTitle: serializer.fromJson<String>(json['semesterTitle']),
      status: serializer.fromJson<int>(json['status']),
      passMarkRequired: serializer.fromJson<int>(json['passMarkRequired']),
      isSiwes: serializer.fromJson<int>(json['isSiwes']),
      hasPreRequisites: serializer.fromJson<int>(json['hasPreRequisites']),
      isUsedBeyondQualifierLevel:
          serializer.fromJson<int>(json['isUsedBeyondQualifierLevel']),
      isUsedInResultComputation:
          serializer.fromJson<int>(json['isUsedInResultComputation']),
      sessionId: serializer.fromJson<int>(json['sessionId']),
      courseId: serializer.fromJson<int>(json['courseId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'creditUnit': serializer.toJson<int>(creditUnit),
      'units': serializer.toJson<String>(units),
      'courseStatus': serializer.toJson<String>(courseStatus),
      'courseStatusId': serializer.toJson<String>(courseStatusId),
      'semesterAccronym': serializer.toJson<String>(semesterAccronym),
      'semesterTitle': serializer.toJson<String>(semesterTitle),
      'status': serializer.toJson<int>(status),
      'passMarkRequired': serializer.toJson<int>(passMarkRequired),
      'isSiwes': serializer.toJson<int>(isSiwes),
      'hasPreRequisites': serializer.toJson<int>(hasPreRequisites),
      'isUsedBeyondQualifierLevel':
          serializer.toJson<int>(isUsedBeyondQualifierLevel),
      'isUsedInResultComputation':
          serializer.toJson<int>(isUsedInResultComputation),
      'sessionId': serializer.toJson<int>(sessionId),
      'courseId': serializer.toJson<int>(courseId),
    };
  }

  CourseSpecificationsTableData copyWith(
          {int? id,
          int? creditUnit,
          String? units,
          String? courseStatus,
          String? courseStatusId,
          String? semesterAccronym,
          String? semesterTitle,
          int? status,
          int? passMarkRequired,
          int? isSiwes,
          int? hasPreRequisites,
          int? isUsedBeyondQualifierLevel,
          int? isUsedInResultComputation,
          int? sessionId,
          int? courseId}) =>
      CourseSpecificationsTableData(
        id: id ?? this.id,
        creditUnit: creditUnit ?? this.creditUnit,
        units: units ?? this.units,
        courseStatus: courseStatus ?? this.courseStatus,
        courseStatusId: courseStatusId ?? this.courseStatusId,
        semesterAccronym: semesterAccronym ?? this.semesterAccronym,
        semesterTitle: semesterTitle ?? this.semesterTitle,
        status: status ?? this.status,
        passMarkRequired: passMarkRequired ?? this.passMarkRequired,
        isSiwes: isSiwes ?? this.isSiwes,
        hasPreRequisites: hasPreRequisites ?? this.hasPreRequisites,
        isUsedBeyondQualifierLevel:
            isUsedBeyondQualifierLevel ?? this.isUsedBeyondQualifierLevel,
        isUsedInResultComputation:
            isUsedInResultComputation ?? this.isUsedInResultComputation,
        sessionId: sessionId ?? this.sessionId,
        courseId: courseId ?? this.courseId,
      );
  CourseSpecificationsTableData copyWithCompanion(
      CourseSpecificationsTableCompanion data) {
    return CourseSpecificationsTableData(
      id: data.id.present ? data.id.value : this.id,
      creditUnit:
          data.creditUnit.present ? data.creditUnit.value : this.creditUnit,
      units: data.units.present ? data.units.value : this.units,
      courseStatus: data.courseStatus.present
          ? data.courseStatus.value
          : this.courseStatus,
      courseStatusId: data.courseStatusId.present
          ? data.courseStatusId.value
          : this.courseStatusId,
      semesterAccronym: data.semesterAccronym.present
          ? data.semesterAccronym.value
          : this.semesterAccronym,
      semesterTitle: data.semesterTitle.present
          ? data.semesterTitle.value
          : this.semesterTitle,
      status: data.status.present ? data.status.value : this.status,
      passMarkRequired: data.passMarkRequired.present
          ? data.passMarkRequired.value
          : this.passMarkRequired,
      isSiwes: data.isSiwes.present ? data.isSiwes.value : this.isSiwes,
      hasPreRequisites: data.hasPreRequisites.present
          ? data.hasPreRequisites.value
          : this.hasPreRequisites,
      isUsedBeyondQualifierLevel: data.isUsedBeyondQualifierLevel.present
          ? data.isUsedBeyondQualifierLevel.value
          : this.isUsedBeyondQualifierLevel,
      isUsedInResultComputation: data.isUsedInResultComputation.present
          ? data.isUsedInResultComputation.value
          : this.isUsedInResultComputation,
      sessionId: data.sessionId.present ? data.sessionId.value : this.sessionId,
      courseId: data.courseId.present ? data.courseId.value : this.courseId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CourseSpecificationsTableData(')
          ..write('id: $id, ')
          ..write('creditUnit: $creditUnit, ')
          ..write('units: $units, ')
          ..write('courseStatus: $courseStatus, ')
          ..write('courseStatusId: $courseStatusId, ')
          ..write('semesterAccronym: $semesterAccronym, ')
          ..write('semesterTitle: $semesterTitle, ')
          ..write('status: $status, ')
          ..write('passMarkRequired: $passMarkRequired, ')
          ..write('isSiwes: $isSiwes, ')
          ..write('hasPreRequisites: $hasPreRequisites, ')
          ..write('isUsedBeyondQualifierLevel: $isUsedBeyondQualifierLevel, ')
          ..write('isUsedInResultComputation: $isUsedInResultComputation, ')
          ..write('sessionId: $sessionId, ')
          ..write('courseId: $courseId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      creditUnit,
      units,
      courseStatus,
      courseStatusId,
      semesterAccronym,
      semesterTitle,
      status,
      passMarkRequired,
      isSiwes,
      hasPreRequisites,
      isUsedBeyondQualifierLevel,
      isUsedInResultComputation,
      sessionId,
      courseId);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CourseSpecificationsTableData &&
          other.id == this.id &&
          other.creditUnit == this.creditUnit &&
          other.units == this.units &&
          other.courseStatus == this.courseStatus &&
          other.courseStatusId == this.courseStatusId &&
          other.semesterAccronym == this.semesterAccronym &&
          other.semesterTitle == this.semesterTitle &&
          other.status == this.status &&
          other.passMarkRequired == this.passMarkRequired &&
          other.isSiwes == this.isSiwes &&
          other.hasPreRequisites == this.hasPreRequisites &&
          other.isUsedBeyondQualifierLevel == this.isUsedBeyondQualifierLevel &&
          other.isUsedInResultComputation == this.isUsedInResultComputation &&
          other.sessionId == this.sessionId &&
          other.courseId == this.courseId);
}

class CourseSpecificationsTableCompanion
    extends UpdateCompanion<CourseSpecificationsTableData> {
  final Value<int> id;
  final Value<int> creditUnit;
  final Value<String> units;
  final Value<String> courseStatus;
  final Value<String> courseStatusId;
  final Value<String> semesterAccronym;
  final Value<String> semesterTitle;
  final Value<int> status;
  final Value<int> passMarkRequired;
  final Value<int> isSiwes;
  final Value<int> hasPreRequisites;
  final Value<int> isUsedBeyondQualifierLevel;
  final Value<int> isUsedInResultComputation;
  final Value<int> sessionId;
  final Value<int> courseId;
  const CourseSpecificationsTableCompanion({
    this.id = const Value.absent(),
    this.creditUnit = const Value.absent(),
    this.units = const Value.absent(),
    this.courseStatus = const Value.absent(),
    this.courseStatusId = const Value.absent(),
    this.semesterAccronym = const Value.absent(),
    this.semesterTitle = const Value.absent(),
    this.status = const Value.absent(),
    this.passMarkRequired = const Value.absent(),
    this.isSiwes = const Value.absent(),
    this.hasPreRequisites = const Value.absent(),
    this.isUsedBeyondQualifierLevel = const Value.absent(),
    this.isUsedInResultComputation = const Value.absent(),
    this.sessionId = const Value.absent(),
    this.courseId = const Value.absent(),
  });
  CourseSpecificationsTableCompanion.insert({
    this.id = const Value.absent(),
    required int creditUnit,
    required String units,
    required String courseStatus,
    required String courseStatusId,
    required String semesterAccronym,
    required String semesterTitle,
    required int status,
    required int passMarkRequired,
    required int isSiwes,
    required int hasPreRequisites,
    required int isUsedBeyondQualifierLevel,
    required int isUsedInResultComputation,
    required int sessionId,
    required int courseId,
  })  : creditUnit = Value(creditUnit),
        units = Value(units),
        courseStatus = Value(courseStatus),
        courseStatusId = Value(courseStatusId),
        semesterAccronym = Value(semesterAccronym),
        semesterTitle = Value(semesterTitle),
        status = Value(status),
        passMarkRequired = Value(passMarkRequired),
        isSiwes = Value(isSiwes),
        hasPreRequisites = Value(hasPreRequisites),
        isUsedBeyondQualifierLevel = Value(isUsedBeyondQualifierLevel),
        isUsedInResultComputation = Value(isUsedInResultComputation),
        sessionId = Value(sessionId),
        courseId = Value(courseId);
  static Insertable<CourseSpecificationsTableData> custom({
    Expression<int>? id,
    Expression<int>? creditUnit,
    Expression<String>? units,
    Expression<String>? courseStatus,
    Expression<String>? courseStatusId,
    Expression<String>? semesterAccronym,
    Expression<String>? semesterTitle,
    Expression<int>? status,
    Expression<int>? passMarkRequired,
    Expression<int>? isSiwes,
    Expression<int>? hasPreRequisites,
    Expression<int>? isUsedBeyondQualifierLevel,
    Expression<int>? isUsedInResultComputation,
    Expression<int>? sessionId,
    Expression<int>? courseId,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (creditUnit != null) 'credit_unit': creditUnit,
      if (units != null) 'units': units,
      if (courseStatus != null) 'course_status': courseStatus,
      if (courseStatusId != null) 'course_status_id': courseStatusId,
      if (semesterAccronym != null) 'semester_accronym': semesterAccronym,
      if (semesterTitle != null) 'semester_title': semesterTitle,
      if (status != null) 'status': status,
      if (passMarkRequired != null) 'pass_mark_required': passMarkRequired,
      if (isSiwes != null) 'is_siwes': isSiwes,
      if (hasPreRequisites != null) 'has_pre_requisites': hasPreRequisites,
      if (isUsedBeyondQualifierLevel != null)
        'is_used_beyond_qualifier_level': isUsedBeyondQualifierLevel,
      if (isUsedInResultComputation != null)
        'is_used_in_result_computation': isUsedInResultComputation,
      if (sessionId != null) 'session_id': sessionId,
      if (courseId != null) 'course_id': courseId,
    });
  }

  CourseSpecificationsTableCompanion copyWith(
      {Value<int>? id,
      Value<int>? creditUnit,
      Value<String>? units,
      Value<String>? courseStatus,
      Value<String>? courseStatusId,
      Value<String>? semesterAccronym,
      Value<String>? semesterTitle,
      Value<int>? status,
      Value<int>? passMarkRequired,
      Value<int>? isSiwes,
      Value<int>? hasPreRequisites,
      Value<int>? isUsedBeyondQualifierLevel,
      Value<int>? isUsedInResultComputation,
      Value<int>? sessionId,
      Value<int>? courseId}) {
    return CourseSpecificationsTableCompanion(
      id: id ?? this.id,
      creditUnit: creditUnit ?? this.creditUnit,
      units: units ?? this.units,
      courseStatus: courseStatus ?? this.courseStatus,
      courseStatusId: courseStatusId ?? this.courseStatusId,
      semesterAccronym: semesterAccronym ?? this.semesterAccronym,
      semesterTitle: semesterTitle ?? this.semesterTitle,
      status: status ?? this.status,
      passMarkRequired: passMarkRequired ?? this.passMarkRequired,
      isSiwes: isSiwes ?? this.isSiwes,
      hasPreRequisites: hasPreRequisites ?? this.hasPreRequisites,
      isUsedBeyondQualifierLevel:
          isUsedBeyondQualifierLevel ?? this.isUsedBeyondQualifierLevel,
      isUsedInResultComputation:
          isUsedInResultComputation ?? this.isUsedInResultComputation,
      sessionId: sessionId ?? this.sessionId,
      courseId: courseId ?? this.courseId,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (creditUnit.present) {
      map['credit_unit'] = Variable<int>(creditUnit.value);
    }
    if (units.present) {
      map['units'] = Variable<String>(units.value);
    }
    if (courseStatus.present) {
      map['course_status'] = Variable<String>(courseStatus.value);
    }
    if (courseStatusId.present) {
      map['course_status_id'] = Variable<String>(courseStatusId.value);
    }
    if (semesterAccronym.present) {
      map['semester_accronym'] = Variable<String>(semesterAccronym.value);
    }
    if (semesterTitle.present) {
      map['semester_title'] = Variable<String>(semesterTitle.value);
    }
    if (status.present) {
      map['status'] = Variable<int>(status.value);
    }
    if (passMarkRequired.present) {
      map['pass_mark_required'] = Variable<int>(passMarkRequired.value);
    }
    if (isSiwes.present) {
      map['is_siwes'] = Variable<int>(isSiwes.value);
    }
    if (hasPreRequisites.present) {
      map['has_pre_requisites'] = Variable<int>(hasPreRequisites.value);
    }
    if (isUsedBeyondQualifierLevel.present) {
      map['is_used_beyond_qualifier_level'] =
          Variable<int>(isUsedBeyondQualifierLevel.value);
    }
    if (isUsedInResultComputation.present) {
      map['is_used_in_result_computation'] =
          Variable<int>(isUsedInResultComputation.value);
    }
    if (sessionId.present) {
      map['session_id'] = Variable<int>(sessionId.value);
    }
    if (courseId.present) {
      map['course_id'] = Variable<int>(courseId.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CourseSpecificationsTableCompanion(')
          ..write('id: $id, ')
          ..write('creditUnit: $creditUnit, ')
          ..write('units: $units, ')
          ..write('courseStatus: $courseStatus, ')
          ..write('courseStatusId: $courseStatusId, ')
          ..write('semesterAccronym: $semesterAccronym, ')
          ..write('semesterTitle: $semesterTitle, ')
          ..write('status: $status, ')
          ..write('passMarkRequired: $passMarkRequired, ')
          ..write('isSiwes: $isSiwes, ')
          ..write('hasPreRequisites: $hasPreRequisites, ')
          ..write('isUsedBeyondQualifierLevel: $isUsedBeyondQualifierLevel, ')
          ..write('isUsedInResultComputation: $isUsedInResultComputation, ')
          ..write('sessionId: $sessionId, ')
          ..write('courseId: $courseId')
          ..write(')'))
        .toString();
  }
}

class $CreditLoadLimitsTableTable extends CreditLoadLimitsTable
    with TableInfo<$CreditLoadLimitsTableTable, CreditLoadLimitsTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CreditLoadLimitsTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _maxCreditUnitMeta =
      const VerificationMeta('maxCreditUnit');
  @override
  late final GeneratedColumn<int> maxCreditUnit = GeneratedColumn<int>(
      'max_credit_unit', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _maxExtraCreditUnitMeta =
      const VerificationMeta('maxExtraCreditUnit');
  @override
  late final GeneratedColumn<int> maxExtraCreditUnit = GeneratedColumn<int>(
      'max_extra_credit_unit', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _minCreditUnitMeta =
      const VerificationMeta('minCreditUnit');
  @override
  late final GeneratedColumn<int> minCreditUnit = GeneratedColumn<int>(
      'min_credit_unit', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _programmeIdMeta =
      const VerificationMeta('programmeId');
  @override
  late final GeneratedColumn<int> programmeId = GeneratedColumn<int>(
      'programme_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _studentAcademicLevelIdMeta =
      const VerificationMeta('studentAcademicLevelId');
  @override
  late final GeneratedColumn<int> studentAcademicLevelId = GeneratedColumn<int>(
      'student_academic_level_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _semesterSettingIdMeta =
      const VerificationMeta('semesterSettingId');
  @override
  late final GeneratedColumn<int> semesterSettingId = GeneratedColumn<int>(
      'semester_setting_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        maxCreditUnit,
        maxExtraCreditUnit,
        minCreditUnit,
        programmeId,
        studentAcademicLevelId,
        semesterSettingId
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'credit_load_limits_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CreditLoadLimitsTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('max_credit_unit')) {
      context.handle(
          _maxCreditUnitMeta,
          maxCreditUnit.isAcceptableOrUnknown(
              data['max_credit_unit']!, _maxCreditUnitMeta));
    } else if (isInserting) {
      context.missing(_maxCreditUnitMeta);
    }
    if (data.containsKey('max_extra_credit_unit')) {
      context.handle(
          _maxExtraCreditUnitMeta,
          maxExtraCreditUnit.isAcceptableOrUnknown(
              data['max_extra_credit_unit']!, _maxExtraCreditUnitMeta));
    } else if (isInserting) {
      context.missing(_maxExtraCreditUnitMeta);
    }
    if (data.containsKey('min_credit_unit')) {
      context.handle(
          _minCreditUnitMeta,
          minCreditUnit.isAcceptableOrUnknown(
              data['min_credit_unit']!, _minCreditUnitMeta));
    } else if (isInserting) {
      context.missing(_minCreditUnitMeta);
    }
    if (data.containsKey('programme_id')) {
      context.handle(
          _programmeIdMeta,
          programmeId.isAcceptableOrUnknown(
              data['programme_id']!, _programmeIdMeta));
    } else if (isInserting) {
      context.missing(_programmeIdMeta);
    }
    if (data.containsKey('student_academic_level_id')) {
      context.handle(
          _studentAcademicLevelIdMeta,
          studentAcademicLevelId.isAcceptableOrUnknown(
              data['student_academic_level_id']!, _studentAcademicLevelIdMeta));
    } else if (isInserting) {
      context.missing(_studentAcademicLevelIdMeta);
    }
    if (data.containsKey('semester_setting_id')) {
      context.handle(
          _semesterSettingIdMeta,
          semesterSettingId.isAcceptableOrUnknown(
              data['semester_setting_id']!, _semesterSettingIdMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CreditLoadLimitsTableData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CreditLoadLimitsTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      maxCreditUnit: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}max_credit_unit'])!,
      maxExtraCreditUnit: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}max_extra_credit_unit'])!,
      minCreditUnit: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}min_credit_unit'])!,
      programmeId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}programme_id'])!,
      studentAcademicLevelId: attachedDatabase.typeMapping.read(
          DriftSqlType.int,
          data['${effectivePrefix}student_academic_level_id'])!,
      semesterSettingId: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}semester_setting_id']),
    );
  }

  @override
  $CreditLoadLimitsTableTable createAlias(String alias) {
    return $CreditLoadLimitsTableTable(attachedDatabase, alias);
  }
}

class CreditLoadLimitsTableData extends DataClass
    implements Insertable<CreditLoadLimitsTableData> {
  final int id;
  final int maxCreditUnit;
  final int maxExtraCreditUnit;
  final int minCreditUnit;
  final int programmeId;
  final int studentAcademicLevelId;
  final int? semesterSettingId;
  const CreditLoadLimitsTableData(
      {required this.id,
      required this.maxCreditUnit,
      required this.maxExtraCreditUnit,
      required this.minCreditUnit,
      required this.programmeId,
      required this.studentAcademicLevelId,
      this.semesterSettingId});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['max_credit_unit'] = Variable<int>(maxCreditUnit);
    map['max_extra_credit_unit'] = Variable<int>(maxExtraCreditUnit);
    map['min_credit_unit'] = Variable<int>(minCreditUnit);
    map['programme_id'] = Variable<int>(programmeId);
    map['student_academic_level_id'] = Variable<int>(studentAcademicLevelId);
    if (!nullToAbsent || semesterSettingId != null) {
      map['semester_setting_id'] = Variable<int>(semesterSettingId);
    }
    return map;
  }

  CreditLoadLimitsTableCompanion toCompanion(bool nullToAbsent) {
    return CreditLoadLimitsTableCompanion(
      id: Value(id),
      maxCreditUnit: Value(maxCreditUnit),
      maxExtraCreditUnit: Value(maxExtraCreditUnit),
      minCreditUnit: Value(minCreditUnit),
      programmeId: Value(programmeId),
      studentAcademicLevelId: Value(studentAcademicLevelId),
      semesterSettingId: semesterSettingId == null && nullToAbsent
          ? const Value.absent()
          : Value(semesterSettingId),
    );
  }

  factory CreditLoadLimitsTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CreditLoadLimitsTableData(
      id: serializer.fromJson<int>(json['id']),
      maxCreditUnit: serializer.fromJson<int>(json['maxCreditUnit']),
      maxExtraCreditUnit: serializer.fromJson<int>(json['maxExtraCreditUnit']),
      minCreditUnit: serializer.fromJson<int>(json['minCreditUnit']),
      programmeId: serializer.fromJson<int>(json['programmeId']),
      studentAcademicLevelId:
          serializer.fromJson<int>(json['studentAcademicLevelId']),
      semesterSettingId: serializer.fromJson<int?>(json['semesterSettingId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'maxCreditUnit': serializer.toJson<int>(maxCreditUnit),
      'maxExtraCreditUnit': serializer.toJson<int>(maxExtraCreditUnit),
      'minCreditUnit': serializer.toJson<int>(minCreditUnit),
      'programmeId': serializer.toJson<int>(programmeId),
      'studentAcademicLevelId': serializer.toJson<int>(studentAcademicLevelId),
      'semesterSettingId': serializer.toJson<int?>(semesterSettingId),
    };
  }

  CreditLoadLimitsTableData copyWith(
          {int? id,
          int? maxCreditUnit,
          int? maxExtraCreditUnit,
          int? minCreditUnit,
          int? programmeId,
          int? studentAcademicLevelId,
          Value<int?> semesterSettingId = const Value.absent()}) =>
      CreditLoadLimitsTableData(
        id: id ?? this.id,
        maxCreditUnit: maxCreditUnit ?? this.maxCreditUnit,
        maxExtraCreditUnit: maxExtraCreditUnit ?? this.maxExtraCreditUnit,
        minCreditUnit: minCreditUnit ?? this.minCreditUnit,
        programmeId: programmeId ?? this.programmeId,
        studentAcademicLevelId:
            studentAcademicLevelId ?? this.studentAcademicLevelId,
        semesterSettingId: semesterSettingId.present
            ? semesterSettingId.value
            : this.semesterSettingId,
      );
  CreditLoadLimitsTableData copyWithCompanion(
      CreditLoadLimitsTableCompanion data) {
    return CreditLoadLimitsTableData(
      id: data.id.present ? data.id.value : this.id,
      maxCreditUnit: data.maxCreditUnit.present
          ? data.maxCreditUnit.value
          : this.maxCreditUnit,
      maxExtraCreditUnit: data.maxExtraCreditUnit.present
          ? data.maxExtraCreditUnit.value
          : this.maxExtraCreditUnit,
      minCreditUnit: data.minCreditUnit.present
          ? data.minCreditUnit.value
          : this.minCreditUnit,
      programmeId:
          data.programmeId.present ? data.programmeId.value : this.programmeId,
      studentAcademicLevelId: data.studentAcademicLevelId.present
          ? data.studentAcademicLevelId.value
          : this.studentAcademicLevelId,
      semesterSettingId: data.semesterSettingId.present
          ? data.semesterSettingId.value
          : this.semesterSettingId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CreditLoadLimitsTableData(')
          ..write('id: $id, ')
          ..write('maxCreditUnit: $maxCreditUnit, ')
          ..write('maxExtraCreditUnit: $maxExtraCreditUnit, ')
          ..write('minCreditUnit: $minCreditUnit, ')
          ..write('programmeId: $programmeId, ')
          ..write('studentAcademicLevelId: $studentAcademicLevelId, ')
          ..write('semesterSettingId: $semesterSettingId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, maxCreditUnit, maxExtraCreditUnit,
      minCreditUnit, programmeId, studentAcademicLevelId, semesterSettingId);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CreditLoadLimitsTableData &&
          other.id == this.id &&
          other.maxCreditUnit == this.maxCreditUnit &&
          other.maxExtraCreditUnit == this.maxExtraCreditUnit &&
          other.minCreditUnit == this.minCreditUnit &&
          other.programmeId == this.programmeId &&
          other.studentAcademicLevelId == this.studentAcademicLevelId &&
          other.semesterSettingId == this.semesterSettingId);
}

class CreditLoadLimitsTableCompanion
    extends UpdateCompanion<CreditLoadLimitsTableData> {
  final Value<int> id;
  final Value<int> maxCreditUnit;
  final Value<int> maxExtraCreditUnit;
  final Value<int> minCreditUnit;
  final Value<int> programmeId;
  final Value<int> studentAcademicLevelId;
  final Value<int?> semesterSettingId;
  const CreditLoadLimitsTableCompanion({
    this.id = const Value.absent(),
    this.maxCreditUnit = const Value.absent(),
    this.maxExtraCreditUnit = const Value.absent(),
    this.minCreditUnit = const Value.absent(),
    this.programmeId = const Value.absent(),
    this.studentAcademicLevelId = const Value.absent(),
    this.semesterSettingId = const Value.absent(),
  });
  CreditLoadLimitsTableCompanion.insert({
    this.id = const Value.absent(),
    required int maxCreditUnit,
    required int maxExtraCreditUnit,
    required int minCreditUnit,
    required int programmeId,
    required int studentAcademicLevelId,
    this.semesterSettingId = const Value.absent(),
  })  : maxCreditUnit = Value(maxCreditUnit),
        maxExtraCreditUnit = Value(maxExtraCreditUnit),
        minCreditUnit = Value(minCreditUnit),
        programmeId = Value(programmeId),
        studentAcademicLevelId = Value(studentAcademicLevelId);
  static Insertable<CreditLoadLimitsTableData> custom({
    Expression<int>? id,
    Expression<int>? maxCreditUnit,
    Expression<int>? maxExtraCreditUnit,
    Expression<int>? minCreditUnit,
    Expression<int>? programmeId,
    Expression<int>? studentAcademicLevelId,
    Expression<int>? semesterSettingId,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (maxCreditUnit != null) 'max_credit_unit': maxCreditUnit,
      if (maxExtraCreditUnit != null)
        'max_extra_credit_unit': maxExtraCreditUnit,
      if (minCreditUnit != null) 'min_credit_unit': minCreditUnit,
      if (programmeId != null) 'programme_id': programmeId,
      if (studentAcademicLevelId != null)
        'student_academic_level_id': studentAcademicLevelId,
      if (semesterSettingId != null) 'semester_setting_id': semesterSettingId,
    });
  }

  CreditLoadLimitsTableCompanion copyWith(
      {Value<int>? id,
      Value<int>? maxCreditUnit,
      Value<int>? maxExtraCreditUnit,
      Value<int>? minCreditUnit,
      Value<int>? programmeId,
      Value<int>? studentAcademicLevelId,
      Value<int?>? semesterSettingId}) {
    return CreditLoadLimitsTableCompanion(
      id: id ?? this.id,
      maxCreditUnit: maxCreditUnit ?? this.maxCreditUnit,
      maxExtraCreditUnit: maxExtraCreditUnit ?? this.maxExtraCreditUnit,
      minCreditUnit: minCreditUnit ?? this.minCreditUnit,
      programmeId: programmeId ?? this.programmeId,
      studentAcademicLevelId:
          studentAcademicLevelId ?? this.studentAcademicLevelId,
      semesterSettingId: semesterSettingId ?? this.semesterSettingId,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (maxCreditUnit.present) {
      map['max_credit_unit'] = Variable<int>(maxCreditUnit.value);
    }
    if (maxExtraCreditUnit.present) {
      map['max_extra_credit_unit'] = Variable<int>(maxExtraCreditUnit.value);
    }
    if (minCreditUnit.present) {
      map['min_credit_unit'] = Variable<int>(minCreditUnit.value);
    }
    if (programmeId.present) {
      map['programme_id'] = Variable<int>(programmeId.value);
    }
    if (studentAcademicLevelId.present) {
      map['student_academic_level_id'] =
          Variable<int>(studentAcademicLevelId.value);
    }
    if (semesterSettingId.present) {
      map['semester_setting_id'] = Variable<int>(semesterSettingId.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CreditLoadLimitsTableCompanion(')
          ..write('id: $id, ')
          ..write('maxCreditUnit: $maxCreditUnit, ')
          ..write('maxExtraCreditUnit: $maxExtraCreditUnit, ')
          ..write('minCreditUnit: $minCreditUnit, ')
          ..write('programmeId: $programmeId, ')
          ..write('studentAcademicLevelId: $studentAcademicLevelId, ')
          ..write('semesterSettingId: $semesterSettingId')
          ..write(')'))
        .toString();
  }
}

abstract class _$LocalDatabase extends GeneratedDatabase {
  _$LocalDatabase(QueryExecutor e) : super(e);
  $LocalDatabaseManager get managers => $LocalDatabaseManager(this);
  late final $AnnouncementsTableTable announcementsTable =
      $AnnouncementsTableTable(this);
  late final $SessionsTableTable sessionsTable = $SessionsTableTable(this);
  late final $SemestersTableTable semestersTable = $SemestersTableTable(this);
  late final $CoursesTableTable coursesTable = $CoursesTableTable(this);
  late final $CourseRegistrationsTableTable courseRegistrationsTable =
      $CourseRegistrationsTableTable(this);
  late final $CourseSpecificationsTableTable courseSpecificationsTable =
      $CourseSpecificationsTableTable(this);
  late final $CreditLoadLimitsTableTable creditLoadLimitsTable =
      $CreditLoadLimitsTableTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        announcementsTable,
        sessionsTable,
        semestersTable,
        coursesTable,
        courseRegistrationsTable,
        courseSpecificationsTable,
        creditLoadLimitsTable
      ];
}

typedef $$AnnouncementsTableTableCreateCompanionBuilder
    = AnnouncementsTableCompanion Function({
  Value<int> id,
  required String title,
  required String message,
  required String priority,
  required String announcer,
  required String recipient,
  required String level,
  Value<String?> programmeLevel,
  required DateTime createdAt,
  required DateTime updatedAt,
});
typedef $$AnnouncementsTableTableUpdateCompanionBuilder
    = AnnouncementsTableCompanion Function({
  Value<int> id,
  Value<String> title,
  Value<String> message,
  Value<String> priority,
  Value<String> announcer,
  Value<String> recipient,
  Value<String> level,
  Value<String?> programmeLevel,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});

class $$AnnouncementsTableTableFilterComposer
    extends Composer<_$LocalDatabase, $AnnouncementsTableTable> {
  $$AnnouncementsTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get priority => $composableBuilder(
      column: $table.priority, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get announcer => $composableBuilder(
      column: $table.announcer, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get recipient => $composableBuilder(
      column: $table.recipient, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get level => $composableBuilder(
      column: $table.level, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get programmeLevel => $composableBuilder(
      column: $table.programmeLevel,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$AnnouncementsTableTableOrderingComposer
    extends Composer<_$LocalDatabase, $AnnouncementsTableTable> {
  $$AnnouncementsTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get priority => $composableBuilder(
      column: $table.priority, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get announcer => $composableBuilder(
      column: $table.announcer, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get recipient => $composableBuilder(
      column: $table.recipient, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get level => $composableBuilder(
      column: $table.level, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get programmeLevel => $composableBuilder(
      column: $table.programmeLevel,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$AnnouncementsTableTableAnnotationComposer
    extends Composer<_$LocalDatabase, $AnnouncementsTableTable> {
  $$AnnouncementsTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  GeneratedColumn<String> get message =>
      $composableBuilder(column: $table.message, builder: (column) => column);

  GeneratedColumn<String> get priority =>
      $composableBuilder(column: $table.priority, builder: (column) => column);

  GeneratedColumn<String> get announcer =>
      $composableBuilder(column: $table.announcer, builder: (column) => column);

  GeneratedColumn<String> get recipient =>
      $composableBuilder(column: $table.recipient, builder: (column) => column);

  GeneratedColumn<String> get level =>
      $composableBuilder(column: $table.level, builder: (column) => column);

  GeneratedColumn<String> get programmeLevel => $composableBuilder(
      column: $table.programmeLevel, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$AnnouncementsTableTableTableManager extends RootTableManager<
    _$LocalDatabase,
    $AnnouncementsTableTable,
    AnnouncementsTableData,
    $$AnnouncementsTableTableFilterComposer,
    $$AnnouncementsTableTableOrderingComposer,
    $$AnnouncementsTableTableAnnotationComposer,
    $$AnnouncementsTableTableCreateCompanionBuilder,
    $$AnnouncementsTableTableUpdateCompanionBuilder,
    (
      AnnouncementsTableData,
      BaseReferences<_$LocalDatabase, $AnnouncementsTableTable,
          AnnouncementsTableData>
    ),
    AnnouncementsTableData,
    PrefetchHooks Function()> {
  $$AnnouncementsTableTableTableManager(
      _$LocalDatabase db, $AnnouncementsTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AnnouncementsTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AnnouncementsTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AnnouncementsTableTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> title = const Value.absent(),
            Value<String> message = const Value.absent(),
            Value<String> priority = const Value.absent(),
            Value<String> announcer = const Value.absent(),
            Value<String> recipient = const Value.absent(),
            Value<String> level = const Value.absent(),
            Value<String?> programmeLevel = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              AnnouncementsTableCompanion(
            id: id,
            title: title,
            message: message,
            priority: priority,
            announcer: announcer,
            recipient: recipient,
            level: level,
            programmeLevel: programmeLevel,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String title,
            required String message,
            required String priority,
            required String announcer,
            required String recipient,
            required String level,
            Value<String?> programmeLevel = const Value.absent(),
            required DateTime createdAt,
            required DateTime updatedAt,
          }) =>
              AnnouncementsTableCompanion.insert(
            id: id,
            title: title,
            message: message,
            priority: priority,
            announcer: announcer,
            recipient: recipient,
            level: level,
            programmeLevel: programmeLevel,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AnnouncementsTableTableProcessedTableManager = ProcessedTableManager<
    _$LocalDatabase,
    $AnnouncementsTableTable,
    AnnouncementsTableData,
    $$AnnouncementsTableTableFilterComposer,
    $$AnnouncementsTableTableOrderingComposer,
    $$AnnouncementsTableTableAnnotationComposer,
    $$AnnouncementsTableTableCreateCompanionBuilder,
    $$AnnouncementsTableTableUpdateCompanionBuilder,
    (
      AnnouncementsTableData,
      BaseReferences<_$LocalDatabase, $AnnouncementsTableTable,
          AnnouncementsTableData>
    ),
    AnnouncementsTableData,
    PrefetchHooks Function()>;
typedef $$SessionsTableTableCreateCompanionBuilder = SessionsTableCompanion
    Function({
  Value<int> id,
  required String sessionName,
  Value<String?> startDate,
  Value<String?> endDate,
  Value<bool?> registrationPaymentStatus,
  Value<bool?> transferRequestStatus,
  Value<bool?> courseRegistrationStatus,
  Value<String?> status,
  Value<int?> position,
  Value<String?> createdAt,
  Value<String?> updatedAt,
  Value<String?> registrationPaymentClosingDate,
  Value<String?> courseRegistrationClosingDate,
});
typedef $$SessionsTableTableUpdateCompanionBuilder = SessionsTableCompanion
    Function({
  Value<int> id,
  Value<String> sessionName,
  Value<String?> startDate,
  Value<String?> endDate,
  Value<bool?> registrationPaymentStatus,
  Value<bool?> transferRequestStatus,
  Value<bool?> courseRegistrationStatus,
  Value<String?> status,
  Value<int?> position,
  Value<String?> createdAt,
  Value<String?> updatedAt,
  Value<String?> registrationPaymentClosingDate,
  Value<String?> courseRegistrationClosingDate,
});

class $$SessionsTableTableFilterComposer
    extends Composer<_$LocalDatabase, $SessionsTableTable> {
  $$SessionsTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get sessionName => $composableBuilder(
      column: $table.sessionName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get startDate => $composableBuilder(
      column: $table.startDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get endDate => $composableBuilder(
      column: $table.endDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get registrationPaymentStatus => $composableBuilder(
      column: $table.registrationPaymentStatus,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get transferRequestStatus => $composableBuilder(
      column: $table.transferRequestStatus,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get courseRegistrationStatus => $composableBuilder(
      column: $table.courseRegistrationStatus,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get position => $composableBuilder(
      column: $table.position, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get registrationPaymentClosingDate =>
      $composableBuilder(
          column: $table.registrationPaymentClosingDate,
          builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get courseRegistrationClosingDate => $composableBuilder(
      column: $table.courseRegistrationClosingDate,
      builder: (column) => ColumnFilters(column));
}

class $$SessionsTableTableOrderingComposer
    extends Composer<_$LocalDatabase, $SessionsTableTable> {
  $$SessionsTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get sessionName => $composableBuilder(
      column: $table.sessionName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get startDate => $composableBuilder(
      column: $table.startDate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get endDate => $composableBuilder(
      column: $table.endDate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get registrationPaymentStatus => $composableBuilder(
      column: $table.registrationPaymentStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get transferRequestStatus => $composableBuilder(
      column: $table.transferRequestStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get courseRegistrationStatus => $composableBuilder(
      column: $table.courseRegistrationStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get position => $composableBuilder(
      column: $table.position, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get registrationPaymentClosingDate =>
      $composableBuilder(
          column: $table.registrationPaymentClosingDate,
          builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get courseRegistrationClosingDate =>
      $composableBuilder(
          column: $table.courseRegistrationClosingDate,
          builder: (column) => ColumnOrderings(column));
}

class $$SessionsTableTableAnnotationComposer
    extends Composer<_$LocalDatabase, $SessionsTableTable> {
  $$SessionsTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get sessionName => $composableBuilder(
      column: $table.sessionName, builder: (column) => column);

  GeneratedColumn<String> get startDate =>
      $composableBuilder(column: $table.startDate, builder: (column) => column);

  GeneratedColumn<String> get endDate =>
      $composableBuilder(column: $table.endDate, builder: (column) => column);

  GeneratedColumn<bool> get registrationPaymentStatus => $composableBuilder(
      column: $table.registrationPaymentStatus, builder: (column) => column);

  GeneratedColumn<bool> get transferRequestStatus => $composableBuilder(
      column: $table.transferRequestStatus, builder: (column) => column);

  GeneratedColumn<bool> get courseRegistrationStatus => $composableBuilder(
      column: $table.courseRegistrationStatus, builder: (column) => column);

  GeneratedColumn<String> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<int> get position =>
      $composableBuilder(column: $table.position, builder: (column) => column);

  GeneratedColumn<String> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<String> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<String> get registrationPaymentClosingDate =>
      $composableBuilder(
          column: $table.registrationPaymentClosingDate,
          builder: (column) => column);

  GeneratedColumn<String> get courseRegistrationClosingDate =>
      $composableBuilder(
          column: $table.courseRegistrationClosingDate,
          builder: (column) => column);
}

class $$SessionsTableTableTableManager extends RootTableManager<
    _$LocalDatabase,
    $SessionsTableTable,
    SessionsTableData,
    $$SessionsTableTableFilterComposer,
    $$SessionsTableTableOrderingComposer,
    $$SessionsTableTableAnnotationComposer,
    $$SessionsTableTableCreateCompanionBuilder,
    $$SessionsTableTableUpdateCompanionBuilder,
    (
      SessionsTableData,
      BaseReferences<_$LocalDatabase, $SessionsTableTable, SessionsTableData>
    ),
    SessionsTableData,
    PrefetchHooks Function()> {
  $$SessionsTableTableTableManager(
      _$LocalDatabase db, $SessionsTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SessionsTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SessionsTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SessionsTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> sessionName = const Value.absent(),
            Value<String?> startDate = const Value.absent(),
            Value<String?> endDate = const Value.absent(),
            Value<bool?> registrationPaymentStatus = const Value.absent(),
            Value<bool?> transferRequestStatus = const Value.absent(),
            Value<bool?> courseRegistrationStatus = const Value.absent(),
            Value<String?> status = const Value.absent(),
            Value<int?> position = const Value.absent(),
            Value<String?> createdAt = const Value.absent(),
            Value<String?> updatedAt = const Value.absent(),
            Value<String?> registrationPaymentClosingDate =
                const Value.absent(),
            Value<String?> courseRegistrationClosingDate = const Value.absent(),
          }) =>
              SessionsTableCompanion(
            id: id,
            sessionName: sessionName,
            startDate: startDate,
            endDate: endDate,
            registrationPaymentStatus: registrationPaymentStatus,
            transferRequestStatus: transferRequestStatus,
            courseRegistrationStatus: courseRegistrationStatus,
            status: status,
            position: position,
            createdAt: createdAt,
            updatedAt: updatedAt,
            registrationPaymentClosingDate: registrationPaymentClosingDate,
            courseRegistrationClosingDate: courseRegistrationClosingDate,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String sessionName,
            Value<String?> startDate = const Value.absent(),
            Value<String?> endDate = const Value.absent(),
            Value<bool?> registrationPaymentStatus = const Value.absent(),
            Value<bool?> transferRequestStatus = const Value.absent(),
            Value<bool?> courseRegistrationStatus = const Value.absent(),
            Value<String?> status = const Value.absent(),
            Value<int?> position = const Value.absent(),
            Value<String?> createdAt = const Value.absent(),
            Value<String?> updatedAt = const Value.absent(),
            Value<String?> registrationPaymentClosingDate =
                const Value.absent(),
            Value<String?> courseRegistrationClosingDate = const Value.absent(),
          }) =>
              SessionsTableCompanion.insert(
            id: id,
            sessionName: sessionName,
            startDate: startDate,
            endDate: endDate,
            registrationPaymentStatus: registrationPaymentStatus,
            transferRequestStatus: transferRequestStatus,
            courseRegistrationStatus: courseRegistrationStatus,
            status: status,
            position: position,
            createdAt: createdAt,
            updatedAt: updatedAt,
            registrationPaymentClosingDate: registrationPaymentClosingDate,
            courseRegistrationClosingDate: courseRegistrationClosingDate,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$SessionsTableTableProcessedTableManager = ProcessedTableManager<
    _$LocalDatabase,
    $SessionsTableTable,
    SessionsTableData,
    $$SessionsTableTableFilterComposer,
    $$SessionsTableTableOrderingComposer,
    $$SessionsTableTableAnnotationComposer,
    $$SessionsTableTableCreateCompanionBuilder,
    $$SessionsTableTableUpdateCompanionBuilder,
    (
      SessionsTableData,
      BaseReferences<_$LocalDatabase, $SessionsTableTable, SessionsTableData>
    ),
    SessionsTableData,
    PrefetchHooks Function()>;
typedef $$SemestersTableTableCreateCompanionBuilder = SemestersTableCompanion
    Function({
  Value<int> id,
  required int sessionId,
  Value<String?> title,
  Value<String?> accronym,
  Value<int?> position,
  Value<String?> startDate,
  Value<String?> endDate,
  Value<String?> lateRegistrationStartDate,
  Value<String?> statusText,
  Value<int?> semesterSettingId,
});
typedef $$SemestersTableTableUpdateCompanionBuilder = SemestersTableCompanion
    Function({
  Value<int> id,
  Value<int> sessionId,
  Value<String?> title,
  Value<String?> accronym,
  Value<int?> position,
  Value<String?> startDate,
  Value<String?> endDate,
  Value<String?> lateRegistrationStartDate,
  Value<String?> statusText,
  Value<int?> semesterSettingId,
});

class $$SemestersTableTableFilterComposer
    extends Composer<_$LocalDatabase, $SemestersTableTable> {
  $$SemestersTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get sessionId => $composableBuilder(
      column: $table.sessionId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get accronym => $composableBuilder(
      column: $table.accronym, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get position => $composableBuilder(
      column: $table.position, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get startDate => $composableBuilder(
      column: $table.startDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get endDate => $composableBuilder(
      column: $table.endDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get lateRegistrationStartDate => $composableBuilder(
      column: $table.lateRegistrationStartDate,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get statusText => $composableBuilder(
      column: $table.statusText, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get semesterSettingId => $composableBuilder(
      column: $table.semesterSettingId,
      builder: (column) => ColumnFilters(column));
}

class $$SemestersTableTableOrderingComposer
    extends Composer<_$LocalDatabase, $SemestersTableTable> {
  $$SemestersTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get sessionId => $composableBuilder(
      column: $table.sessionId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get accronym => $composableBuilder(
      column: $table.accronym, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get position => $composableBuilder(
      column: $table.position, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get startDate => $composableBuilder(
      column: $table.startDate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get endDate => $composableBuilder(
      column: $table.endDate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get lateRegistrationStartDate => $composableBuilder(
      column: $table.lateRegistrationStartDate,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get statusText => $composableBuilder(
      column: $table.statusText, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get semesterSettingId => $composableBuilder(
      column: $table.semesterSettingId,
      builder: (column) => ColumnOrderings(column));
}

class $$SemestersTableTableAnnotationComposer
    extends Composer<_$LocalDatabase, $SemestersTableTable> {
  $$SemestersTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get sessionId =>
      $composableBuilder(column: $table.sessionId, builder: (column) => column);

  GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  GeneratedColumn<String> get accronym =>
      $composableBuilder(column: $table.accronym, builder: (column) => column);

  GeneratedColumn<int> get position =>
      $composableBuilder(column: $table.position, builder: (column) => column);

  GeneratedColumn<String> get startDate =>
      $composableBuilder(column: $table.startDate, builder: (column) => column);

  GeneratedColumn<String> get endDate =>
      $composableBuilder(column: $table.endDate, builder: (column) => column);

  GeneratedColumn<String> get lateRegistrationStartDate => $composableBuilder(
      column: $table.lateRegistrationStartDate, builder: (column) => column);

  GeneratedColumn<String> get statusText => $composableBuilder(
      column: $table.statusText, builder: (column) => column);

  GeneratedColumn<int> get semesterSettingId => $composableBuilder(
      column: $table.semesterSettingId, builder: (column) => column);
}

class $$SemestersTableTableTableManager extends RootTableManager<
    _$LocalDatabase,
    $SemestersTableTable,
    SemestersTableData,
    $$SemestersTableTableFilterComposer,
    $$SemestersTableTableOrderingComposer,
    $$SemestersTableTableAnnotationComposer,
    $$SemestersTableTableCreateCompanionBuilder,
    $$SemestersTableTableUpdateCompanionBuilder,
    (
      SemestersTableData,
      BaseReferences<_$LocalDatabase, $SemestersTableTable, SemestersTableData>
    ),
    SemestersTableData,
    PrefetchHooks Function()> {
  $$SemestersTableTableTableManager(
      _$LocalDatabase db, $SemestersTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SemestersTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SemestersTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SemestersTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> sessionId = const Value.absent(),
            Value<String?> title = const Value.absent(),
            Value<String?> accronym = const Value.absent(),
            Value<int?> position = const Value.absent(),
            Value<String?> startDate = const Value.absent(),
            Value<String?> endDate = const Value.absent(),
            Value<String?> lateRegistrationStartDate = const Value.absent(),
            Value<String?> statusText = const Value.absent(),
            Value<int?> semesterSettingId = const Value.absent(),
          }) =>
              SemestersTableCompanion(
            id: id,
            sessionId: sessionId,
            title: title,
            accronym: accronym,
            position: position,
            startDate: startDate,
            endDate: endDate,
            lateRegistrationStartDate: lateRegistrationStartDate,
            statusText: statusText,
            semesterSettingId: semesterSettingId,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int sessionId,
            Value<String?> title = const Value.absent(),
            Value<String?> accronym = const Value.absent(),
            Value<int?> position = const Value.absent(),
            Value<String?> startDate = const Value.absent(),
            Value<String?> endDate = const Value.absent(),
            Value<String?> lateRegistrationStartDate = const Value.absent(),
            Value<String?> statusText = const Value.absent(),
            Value<int?> semesterSettingId = const Value.absent(),
          }) =>
              SemestersTableCompanion.insert(
            id: id,
            sessionId: sessionId,
            title: title,
            accronym: accronym,
            position: position,
            startDate: startDate,
            endDate: endDate,
            lateRegistrationStartDate: lateRegistrationStartDate,
            statusText: statusText,
            semesterSettingId: semesterSettingId,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$SemestersTableTableProcessedTableManager = ProcessedTableManager<
    _$LocalDatabase,
    $SemestersTableTable,
    SemestersTableData,
    $$SemestersTableTableFilterComposer,
    $$SemestersTableTableOrderingComposer,
    $$SemestersTableTableAnnotationComposer,
    $$SemestersTableTableCreateCompanionBuilder,
    $$SemestersTableTableUpdateCompanionBuilder,
    (
      SemestersTableData,
      BaseReferences<_$LocalDatabase, $SemestersTableTable, SemestersTableData>
    ),
    SemestersTableData,
    PrefetchHooks Function()>;
typedef $$CoursesTableTableCreateCompanionBuilder = CoursesTableCompanion
    Function({
  Value<int> id,
  required String courseCode,
  required String courseTitle,
  required int creditUnit,
  Value<String?> courseSynopsis,
  required String type,
  Value<int?> hostId,
  Value<int?> departmentId,
  Value<int?> passMark,
  Value<String?> lecturers,
});
typedef $$CoursesTableTableUpdateCompanionBuilder = CoursesTableCompanion
    Function({
  Value<int> id,
  Value<String> courseCode,
  Value<String> courseTitle,
  Value<int> creditUnit,
  Value<String?> courseSynopsis,
  Value<String> type,
  Value<int?> hostId,
  Value<int?> departmentId,
  Value<int?> passMark,
  Value<String?> lecturers,
});

class $$CoursesTableTableFilterComposer
    extends Composer<_$LocalDatabase, $CoursesTableTable> {
  $$CoursesTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get courseCode => $composableBuilder(
      column: $table.courseCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get courseTitle => $composableBuilder(
      column: $table.courseTitle, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get creditUnit => $composableBuilder(
      column: $table.creditUnit, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get courseSynopsis => $composableBuilder(
      column: $table.courseSynopsis,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get hostId => $composableBuilder(
      column: $table.hostId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get departmentId => $composableBuilder(
      column: $table.departmentId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get passMark => $composableBuilder(
      column: $table.passMark, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get lecturers => $composableBuilder(
      column: $table.lecturers, builder: (column) => ColumnFilters(column));
}

class $$CoursesTableTableOrderingComposer
    extends Composer<_$LocalDatabase, $CoursesTableTable> {
  $$CoursesTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get courseCode => $composableBuilder(
      column: $table.courseCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get courseTitle => $composableBuilder(
      column: $table.courseTitle, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get creditUnit => $composableBuilder(
      column: $table.creditUnit, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get courseSynopsis => $composableBuilder(
      column: $table.courseSynopsis,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get hostId => $composableBuilder(
      column: $table.hostId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get departmentId => $composableBuilder(
      column: $table.departmentId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get passMark => $composableBuilder(
      column: $table.passMark, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get lecturers => $composableBuilder(
      column: $table.lecturers, builder: (column) => ColumnOrderings(column));
}

class $$CoursesTableTableAnnotationComposer
    extends Composer<_$LocalDatabase, $CoursesTableTable> {
  $$CoursesTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get courseCode => $composableBuilder(
      column: $table.courseCode, builder: (column) => column);

  GeneratedColumn<String> get courseTitle => $composableBuilder(
      column: $table.courseTitle, builder: (column) => column);

  GeneratedColumn<int> get creditUnit => $composableBuilder(
      column: $table.creditUnit, builder: (column) => column);

  GeneratedColumn<String> get courseSynopsis => $composableBuilder(
      column: $table.courseSynopsis, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<int> get hostId =>
      $composableBuilder(column: $table.hostId, builder: (column) => column);

  GeneratedColumn<int> get departmentId => $composableBuilder(
      column: $table.departmentId, builder: (column) => column);

  GeneratedColumn<int> get passMark =>
      $composableBuilder(column: $table.passMark, builder: (column) => column);

  GeneratedColumn<String> get lecturers =>
      $composableBuilder(column: $table.lecturers, builder: (column) => column);
}

class $$CoursesTableTableTableManager extends RootTableManager<
    _$LocalDatabase,
    $CoursesTableTable,
    CoursesTableData,
    $$CoursesTableTableFilterComposer,
    $$CoursesTableTableOrderingComposer,
    $$CoursesTableTableAnnotationComposer,
    $$CoursesTableTableCreateCompanionBuilder,
    $$CoursesTableTableUpdateCompanionBuilder,
    (
      CoursesTableData,
      BaseReferences<_$LocalDatabase, $CoursesTableTable, CoursesTableData>
    ),
    CoursesTableData,
    PrefetchHooks Function()> {
  $$CoursesTableTableTableManager(_$LocalDatabase db, $CoursesTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CoursesTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CoursesTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CoursesTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> courseCode = const Value.absent(),
            Value<String> courseTitle = const Value.absent(),
            Value<int> creditUnit = const Value.absent(),
            Value<String?> courseSynopsis = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<int?> hostId = const Value.absent(),
            Value<int?> departmentId = const Value.absent(),
            Value<int?> passMark = const Value.absent(),
            Value<String?> lecturers = const Value.absent(),
          }) =>
              CoursesTableCompanion(
            id: id,
            courseCode: courseCode,
            courseTitle: courseTitle,
            creditUnit: creditUnit,
            courseSynopsis: courseSynopsis,
            type: type,
            hostId: hostId,
            departmentId: departmentId,
            passMark: passMark,
            lecturers: lecturers,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String courseCode,
            required String courseTitle,
            required int creditUnit,
            Value<String?> courseSynopsis = const Value.absent(),
            required String type,
            Value<int?> hostId = const Value.absent(),
            Value<int?> departmentId = const Value.absent(),
            Value<int?> passMark = const Value.absent(),
            Value<String?> lecturers = const Value.absent(),
          }) =>
              CoursesTableCompanion.insert(
            id: id,
            courseCode: courseCode,
            courseTitle: courseTitle,
            creditUnit: creditUnit,
            courseSynopsis: courseSynopsis,
            type: type,
            hostId: hostId,
            departmentId: departmentId,
            passMark: passMark,
            lecturers: lecturers,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CoursesTableTableProcessedTableManager = ProcessedTableManager<
    _$LocalDatabase,
    $CoursesTableTable,
    CoursesTableData,
    $$CoursesTableTableFilterComposer,
    $$CoursesTableTableOrderingComposer,
    $$CoursesTableTableAnnotationComposer,
    $$CoursesTableTableCreateCompanionBuilder,
    $$CoursesTableTableUpdateCompanionBuilder,
    (
      CoursesTableData,
      BaseReferences<_$LocalDatabase, $CoursesTableTable, CoursesTableData>
    ),
    CoursesTableData,
    PrefetchHooks Function()>;
typedef $$CourseRegistrationsTableTableCreateCompanionBuilder
    = CourseRegistrationsTableCompanion Function({
  Value<int> id,
  required int courseId,
  Value<int?> sessionId,
  Value<String?> semesterAccronym,
  Value<int?> semesterPosition,
  Value<String?> semesterTitle,
  Value<String?> score,
  Value<String?> courseStatus,
  Value<String?> approvalStatus,
  Value<String?> approvalRemark,
  Value<String?> remark,
  Value<String?> createdAt,
  Value<String?> updatedAt,
});
typedef $$CourseRegistrationsTableTableUpdateCompanionBuilder
    = CourseRegistrationsTableCompanion Function({
  Value<int> id,
  Value<int> courseId,
  Value<int?> sessionId,
  Value<String?> semesterAccronym,
  Value<int?> semesterPosition,
  Value<String?> semesterTitle,
  Value<String?> score,
  Value<String?> courseStatus,
  Value<String?> approvalStatus,
  Value<String?> approvalRemark,
  Value<String?> remark,
  Value<String?> createdAt,
  Value<String?> updatedAt,
});

class $$CourseRegistrationsTableTableFilterComposer
    extends Composer<_$LocalDatabase, $CourseRegistrationsTableTable> {
  $$CourseRegistrationsTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get courseId => $composableBuilder(
      column: $table.courseId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get sessionId => $composableBuilder(
      column: $table.sessionId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get semesterAccronym => $composableBuilder(
      column: $table.semesterAccronym,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get semesterPosition => $composableBuilder(
      column: $table.semesterPosition,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get semesterTitle => $composableBuilder(
      column: $table.semesterTitle, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get score => $composableBuilder(
      column: $table.score, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get courseStatus => $composableBuilder(
      column: $table.courseStatus, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get approvalStatus => $composableBuilder(
      column: $table.approvalStatus,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get approvalRemark => $composableBuilder(
      column: $table.approvalRemark,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get remark => $composableBuilder(
      column: $table.remark, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$CourseRegistrationsTableTableOrderingComposer
    extends Composer<_$LocalDatabase, $CourseRegistrationsTableTable> {
  $$CourseRegistrationsTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get courseId => $composableBuilder(
      column: $table.courseId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get sessionId => $composableBuilder(
      column: $table.sessionId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get semesterAccronym => $composableBuilder(
      column: $table.semesterAccronym,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get semesterPosition => $composableBuilder(
      column: $table.semesterPosition,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get semesterTitle => $composableBuilder(
      column: $table.semesterTitle,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get score => $composableBuilder(
      column: $table.score, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get courseStatus => $composableBuilder(
      column: $table.courseStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get approvalStatus => $composableBuilder(
      column: $table.approvalStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get approvalRemark => $composableBuilder(
      column: $table.approvalRemark,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get remark => $composableBuilder(
      column: $table.remark, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$CourseRegistrationsTableTableAnnotationComposer
    extends Composer<_$LocalDatabase, $CourseRegistrationsTableTable> {
  $$CourseRegistrationsTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get courseId =>
      $composableBuilder(column: $table.courseId, builder: (column) => column);

  GeneratedColumn<int> get sessionId =>
      $composableBuilder(column: $table.sessionId, builder: (column) => column);

  GeneratedColumn<String> get semesterAccronym => $composableBuilder(
      column: $table.semesterAccronym, builder: (column) => column);

  GeneratedColumn<int> get semesterPosition => $composableBuilder(
      column: $table.semesterPosition, builder: (column) => column);

  GeneratedColumn<String> get semesterTitle => $composableBuilder(
      column: $table.semesterTitle, builder: (column) => column);

  GeneratedColumn<String> get score =>
      $composableBuilder(column: $table.score, builder: (column) => column);

  GeneratedColumn<String> get courseStatus => $composableBuilder(
      column: $table.courseStatus, builder: (column) => column);

  GeneratedColumn<String> get approvalStatus => $composableBuilder(
      column: $table.approvalStatus, builder: (column) => column);

  GeneratedColumn<String> get approvalRemark => $composableBuilder(
      column: $table.approvalRemark, builder: (column) => column);

  GeneratedColumn<String> get remark =>
      $composableBuilder(column: $table.remark, builder: (column) => column);

  GeneratedColumn<String> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<String> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$CourseRegistrationsTableTableTableManager extends RootTableManager<
    _$LocalDatabase,
    $CourseRegistrationsTableTable,
    CourseRegistrationsTableData,
    $$CourseRegistrationsTableTableFilterComposer,
    $$CourseRegistrationsTableTableOrderingComposer,
    $$CourseRegistrationsTableTableAnnotationComposer,
    $$CourseRegistrationsTableTableCreateCompanionBuilder,
    $$CourseRegistrationsTableTableUpdateCompanionBuilder,
    (
      CourseRegistrationsTableData,
      BaseReferences<_$LocalDatabase, $CourseRegistrationsTableTable,
          CourseRegistrationsTableData>
    ),
    CourseRegistrationsTableData,
    PrefetchHooks Function()> {
  $$CourseRegistrationsTableTableTableManager(
      _$LocalDatabase db, $CourseRegistrationsTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CourseRegistrationsTableTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$CourseRegistrationsTableTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CourseRegistrationsTableTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> courseId = const Value.absent(),
            Value<int?> sessionId = const Value.absent(),
            Value<String?> semesterAccronym = const Value.absent(),
            Value<int?> semesterPosition = const Value.absent(),
            Value<String?> semesterTitle = const Value.absent(),
            Value<String?> score = const Value.absent(),
            Value<String?> courseStatus = const Value.absent(),
            Value<String?> approvalStatus = const Value.absent(),
            Value<String?> approvalRemark = const Value.absent(),
            Value<String?> remark = const Value.absent(),
            Value<String?> createdAt = const Value.absent(),
            Value<String?> updatedAt = const Value.absent(),
          }) =>
              CourseRegistrationsTableCompanion(
            id: id,
            courseId: courseId,
            sessionId: sessionId,
            semesterAccronym: semesterAccronym,
            semesterPosition: semesterPosition,
            semesterTitle: semesterTitle,
            score: score,
            courseStatus: courseStatus,
            approvalStatus: approvalStatus,
            approvalRemark: approvalRemark,
            remark: remark,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int courseId,
            Value<int?> sessionId = const Value.absent(),
            Value<String?> semesterAccronym = const Value.absent(),
            Value<int?> semesterPosition = const Value.absent(),
            Value<String?> semesterTitle = const Value.absent(),
            Value<String?> score = const Value.absent(),
            Value<String?> courseStatus = const Value.absent(),
            Value<String?> approvalStatus = const Value.absent(),
            Value<String?> approvalRemark = const Value.absent(),
            Value<String?> remark = const Value.absent(),
            Value<String?> createdAt = const Value.absent(),
            Value<String?> updatedAt = const Value.absent(),
          }) =>
              CourseRegistrationsTableCompanion.insert(
            id: id,
            courseId: courseId,
            sessionId: sessionId,
            semesterAccronym: semesterAccronym,
            semesterPosition: semesterPosition,
            semesterTitle: semesterTitle,
            score: score,
            courseStatus: courseStatus,
            approvalStatus: approvalStatus,
            approvalRemark: approvalRemark,
            remark: remark,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CourseRegistrationsTableTableProcessedTableManager
    = ProcessedTableManager<
        _$LocalDatabase,
        $CourseRegistrationsTableTable,
        CourseRegistrationsTableData,
        $$CourseRegistrationsTableTableFilterComposer,
        $$CourseRegistrationsTableTableOrderingComposer,
        $$CourseRegistrationsTableTableAnnotationComposer,
        $$CourseRegistrationsTableTableCreateCompanionBuilder,
        $$CourseRegistrationsTableTableUpdateCompanionBuilder,
        (
          CourseRegistrationsTableData,
          BaseReferences<_$LocalDatabase, $CourseRegistrationsTableTable,
              CourseRegistrationsTableData>
        ),
        CourseRegistrationsTableData,
        PrefetchHooks Function()>;
typedef $$CourseSpecificationsTableTableCreateCompanionBuilder
    = CourseSpecificationsTableCompanion Function({
  Value<int> id,
  required int creditUnit,
  required String units,
  required String courseStatus,
  required String courseStatusId,
  required String semesterAccronym,
  required String semesterTitle,
  required int status,
  required int passMarkRequired,
  required int isSiwes,
  required int hasPreRequisites,
  required int isUsedBeyondQualifierLevel,
  required int isUsedInResultComputation,
  required int sessionId,
  required int courseId,
});
typedef $$CourseSpecificationsTableTableUpdateCompanionBuilder
    = CourseSpecificationsTableCompanion Function({
  Value<int> id,
  Value<int> creditUnit,
  Value<String> units,
  Value<String> courseStatus,
  Value<String> courseStatusId,
  Value<String> semesterAccronym,
  Value<String> semesterTitle,
  Value<int> status,
  Value<int> passMarkRequired,
  Value<int> isSiwes,
  Value<int> hasPreRequisites,
  Value<int> isUsedBeyondQualifierLevel,
  Value<int> isUsedInResultComputation,
  Value<int> sessionId,
  Value<int> courseId,
});

class $$CourseSpecificationsTableTableFilterComposer
    extends Composer<_$LocalDatabase, $CourseSpecificationsTableTable> {
  $$CourseSpecificationsTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get creditUnit => $composableBuilder(
      column: $table.creditUnit, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get units => $composableBuilder(
      column: $table.units, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get courseStatus => $composableBuilder(
      column: $table.courseStatus, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get courseStatusId => $composableBuilder(
      column: $table.courseStatusId,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get semesterAccronym => $composableBuilder(
      column: $table.semesterAccronym,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get semesterTitle => $composableBuilder(
      column: $table.semesterTitle, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get passMarkRequired => $composableBuilder(
      column: $table.passMarkRequired,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get isSiwes => $composableBuilder(
      column: $table.isSiwes, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get hasPreRequisites => $composableBuilder(
      column: $table.hasPreRequisites,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get isUsedBeyondQualifierLevel => $composableBuilder(
      column: $table.isUsedBeyondQualifierLevel,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get isUsedInResultComputation => $composableBuilder(
      column: $table.isUsedInResultComputation,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get sessionId => $composableBuilder(
      column: $table.sessionId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get courseId => $composableBuilder(
      column: $table.courseId, builder: (column) => ColumnFilters(column));
}

class $$CourseSpecificationsTableTableOrderingComposer
    extends Composer<_$LocalDatabase, $CourseSpecificationsTableTable> {
  $$CourseSpecificationsTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get creditUnit => $composableBuilder(
      column: $table.creditUnit, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get units => $composableBuilder(
      column: $table.units, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get courseStatus => $composableBuilder(
      column: $table.courseStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get courseStatusId => $composableBuilder(
      column: $table.courseStatusId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get semesterAccronym => $composableBuilder(
      column: $table.semesterAccronym,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get semesterTitle => $composableBuilder(
      column: $table.semesterTitle,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get passMarkRequired => $composableBuilder(
      column: $table.passMarkRequired,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get isSiwes => $composableBuilder(
      column: $table.isSiwes, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get hasPreRequisites => $composableBuilder(
      column: $table.hasPreRequisites,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get isUsedBeyondQualifierLevel => $composableBuilder(
      column: $table.isUsedBeyondQualifierLevel,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get isUsedInResultComputation => $composableBuilder(
      column: $table.isUsedInResultComputation,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get sessionId => $composableBuilder(
      column: $table.sessionId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get courseId => $composableBuilder(
      column: $table.courseId, builder: (column) => ColumnOrderings(column));
}

class $$CourseSpecificationsTableTableAnnotationComposer
    extends Composer<_$LocalDatabase, $CourseSpecificationsTableTable> {
  $$CourseSpecificationsTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get creditUnit => $composableBuilder(
      column: $table.creditUnit, builder: (column) => column);

  GeneratedColumn<String> get units =>
      $composableBuilder(column: $table.units, builder: (column) => column);

  GeneratedColumn<String> get courseStatus => $composableBuilder(
      column: $table.courseStatus, builder: (column) => column);

  GeneratedColumn<String> get courseStatusId => $composableBuilder(
      column: $table.courseStatusId, builder: (column) => column);

  GeneratedColumn<String> get semesterAccronym => $composableBuilder(
      column: $table.semesterAccronym, builder: (column) => column);

  GeneratedColumn<String> get semesterTitle => $composableBuilder(
      column: $table.semesterTitle, builder: (column) => column);

  GeneratedColumn<int> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<int> get passMarkRequired => $composableBuilder(
      column: $table.passMarkRequired, builder: (column) => column);

  GeneratedColumn<int> get isSiwes =>
      $composableBuilder(column: $table.isSiwes, builder: (column) => column);

  GeneratedColumn<int> get hasPreRequisites => $composableBuilder(
      column: $table.hasPreRequisites, builder: (column) => column);

  GeneratedColumn<int> get isUsedBeyondQualifierLevel => $composableBuilder(
      column: $table.isUsedBeyondQualifierLevel, builder: (column) => column);

  GeneratedColumn<int> get isUsedInResultComputation => $composableBuilder(
      column: $table.isUsedInResultComputation, builder: (column) => column);

  GeneratedColumn<int> get sessionId =>
      $composableBuilder(column: $table.sessionId, builder: (column) => column);

  GeneratedColumn<int> get courseId =>
      $composableBuilder(column: $table.courseId, builder: (column) => column);
}

class $$CourseSpecificationsTableTableTableManager extends RootTableManager<
    _$LocalDatabase,
    $CourseSpecificationsTableTable,
    CourseSpecificationsTableData,
    $$CourseSpecificationsTableTableFilterComposer,
    $$CourseSpecificationsTableTableOrderingComposer,
    $$CourseSpecificationsTableTableAnnotationComposer,
    $$CourseSpecificationsTableTableCreateCompanionBuilder,
    $$CourseSpecificationsTableTableUpdateCompanionBuilder,
    (
      CourseSpecificationsTableData,
      BaseReferences<_$LocalDatabase, $CourseSpecificationsTableTable,
          CourseSpecificationsTableData>
    ),
    CourseSpecificationsTableData,
    PrefetchHooks Function()> {
  $$CourseSpecificationsTableTableTableManager(
      _$LocalDatabase db, $CourseSpecificationsTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CourseSpecificationsTableTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$CourseSpecificationsTableTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CourseSpecificationsTableTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> creditUnit = const Value.absent(),
            Value<String> units = const Value.absent(),
            Value<String> courseStatus = const Value.absent(),
            Value<String> courseStatusId = const Value.absent(),
            Value<String> semesterAccronym = const Value.absent(),
            Value<String> semesterTitle = const Value.absent(),
            Value<int> status = const Value.absent(),
            Value<int> passMarkRequired = const Value.absent(),
            Value<int> isSiwes = const Value.absent(),
            Value<int> hasPreRequisites = const Value.absent(),
            Value<int> isUsedBeyondQualifierLevel = const Value.absent(),
            Value<int> isUsedInResultComputation = const Value.absent(),
            Value<int> sessionId = const Value.absent(),
            Value<int> courseId = const Value.absent(),
          }) =>
              CourseSpecificationsTableCompanion(
            id: id,
            creditUnit: creditUnit,
            units: units,
            courseStatus: courseStatus,
            courseStatusId: courseStatusId,
            semesterAccronym: semesterAccronym,
            semesterTitle: semesterTitle,
            status: status,
            passMarkRequired: passMarkRequired,
            isSiwes: isSiwes,
            hasPreRequisites: hasPreRequisites,
            isUsedBeyondQualifierLevel: isUsedBeyondQualifierLevel,
            isUsedInResultComputation: isUsedInResultComputation,
            sessionId: sessionId,
            courseId: courseId,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int creditUnit,
            required String units,
            required String courseStatus,
            required String courseStatusId,
            required String semesterAccronym,
            required String semesterTitle,
            required int status,
            required int passMarkRequired,
            required int isSiwes,
            required int hasPreRequisites,
            required int isUsedBeyondQualifierLevel,
            required int isUsedInResultComputation,
            required int sessionId,
            required int courseId,
          }) =>
              CourseSpecificationsTableCompanion.insert(
            id: id,
            creditUnit: creditUnit,
            units: units,
            courseStatus: courseStatus,
            courseStatusId: courseStatusId,
            semesterAccronym: semesterAccronym,
            semesterTitle: semesterTitle,
            status: status,
            passMarkRequired: passMarkRequired,
            isSiwes: isSiwes,
            hasPreRequisites: hasPreRequisites,
            isUsedBeyondQualifierLevel: isUsedBeyondQualifierLevel,
            isUsedInResultComputation: isUsedInResultComputation,
            sessionId: sessionId,
            courseId: courseId,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CourseSpecificationsTableTableProcessedTableManager
    = ProcessedTableManager<
        _$LocalDatabase,
        $CourseSpecificationsTableTable,
        CourseSpecificationsTableData,
        $$CourseSpecificationsTableTableFilterComposer,
        $$CourseSpecificationsTableTableOrderingComposer,
        $$CourseSpecificationsTableTableAnnotationComposer,
        $$CourseSpecificationsTableTableCreateCompanionBuilder,
        $$CourseSpecificationsTableTableUpdateCompanionBuilder,
        (
          CourseSpecificationsTableData,
          BaseReferences<_$LocalDatabase, $CourseSpecificationsTableTable,
              CourseSpecificationsTableData>
        ),
        CourseSpecificationsTableData,
        PrefetchHooks Function()>;
typedef $$CreditLoadLimitsTableTableCreateCompanionBuilder
    = CreditLoadLimitsTableCompanion Function({
  Value<int> id,
  required int maxCreditUnit,
  required int maxExtraCreditUnit,
  required int minCreditUnit,
  required int programmeId,
  required int studentAcademicLevelId,
  Value<int?> semesterSettingId,
});
typedef $$CreditLoadLimitsTableTableUpdateCompanionBuilder
    = CreditLoadLimitsTableCompanion Function({
  Value<int> id,
  Value<int> maxCreditUnit,
  Value<int> maxExtraCreditUnit,
  Value<int> minCreditUnit,
  Value<int> programmeId,
  Value<int> studentAcademicLevelId,
  Value<int?> semesterSettingId,
});

class $$CreditLoadLimitsTableTableFilterComposer
    extends Composer<_$LocalDatabase, $CreditLoadLimitsTableTable> {
  $$CreditLoadLimitsTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get maxCreditUnit => $composableBuilder(
      column: $table.maxCreditUnit, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get maxExtraCreditUnit => $composableBuilder(
      column: $table.maxExtraCreditUnit,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get minCreditUnit => $composableBuilder(
      column: $table.minCreditUnit, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get programmeId => $composableBuilder(
      column: $table.programmeId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get studentAcademicLevelId => $composableBuilder(
      column: $table.studentAcademicLevelId,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get semesterSettingId => $composableBuilder(
      column: $table.semesterSettingId,
      builder: (column) => ColumnFilters(column));
}

class $$CreditLoadLimitsTableTableOrderingComposer
    extends Composer<_$LocalDatabase, $CreditLoadLimitsTableTable> {
  $$CreditLoadLimitsTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get maxCreditUnit => $composableBuilder(
      column: $table.maxCreditUnit,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get maxExtraCreditUnit => $composableBuilder(
      column: $table.maxExtraCreditUnit,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get minCreditUnit => $composableBuilder(
      column: $table.minCreditUnit,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get programmeId => $composableBuilder(
      column: $table.programmeId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get studentAcademicLevelId => $composableBuilder(
      column: $table.studentAcademicLevelId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get semesterSettingId => $composableBuilder(
      column: $table.semesterSettingId,
      builder: (column) => ColumnOrderings(column));
}

class $$CreditLoadLimitsTableTableAnnotationComposer
    extends Composer<_$LocalDatabase, $CreditLoadLimitsTableTable> {
  $$CreditLoadLimitsTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get maxCreditUnit => $composableBuilder(
      column: $table.maxCreditUnit, builder: (column) => column);

  GeneratedColumn<int> get maxExtraCreditUnit => $composableBuilder(
      column: $table.maxExtraCreditUnit, builder: (column) => column);

  GeneratedColumn<int> get minCreditUnit => $composableBuilder(
      column: $table.minCreditUnit, builder: (column) => column);

  GeneratedColumn<int> get programmeId => $composableBuilder(
      column: $table.programmeId, builder: (column) => column);

  GeneratedColumn<int> get studentAcademicLevelId => $composableBuilder(
      column: $table.studentAcademicLevelId, builder: (column) => column);

  GeneratedColumn<int> get semesterSettingId => $composableBuilder(
      column: $table.semesterSettingId, builder: (column) => column);
}

class $$CreditLoadLimitsTableTableTableManager extends RootTableManager<
    _$LocalDatabase,
    $CreditLoadLimitsTableTable,
    CreditLoadLimitsTableData,
    $$CreditLoadLimitsTableTableFilterComposer,
    $$CreditLoadLimitsTableTableOrderingComposer,
    $$CreditLoadLimitsTableTableAnnotationComposer,
    $$CreditLoadLimitsTableTableCreateCompanionBuilder,
    $$CreditLoadLimitsTableTableUpdateCompanionBuilder,
    (
      CreditLoadLimitsTableData,
      BaseReferences<_$LocalDatabase, $CreditLoadLimitsTableTable,
          CreditLoadLimitsTableData>
    ),
    CreditLoadLimitsTableData,
    PrefetchHooks Function()> {
  $$CreditLoadLimitsTableTableTableManager(
      _$LocalDatabase db, $CreditLoadLimitsTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CreditLoadLimitsTableTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$CreditLoadLimitsTableTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CreditLoadLimitsTableTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> maxCreditUnit = const Value.absent(),
            Value<int> maxExtraCreditUnit = const Value.absent(),
            Value<int> minCreditUnit = const Value.absent(),
            Value<int> programmeId = const Value.absent(),
            Value<int> studentAcademicLevelId = const Value.absent(),
            Value<int?> semesterSettingId = const Value.absent(),
          }) =>
              CreditLoadLimitsTableCompanion(
            id: id,
            maxCreditUnit: maxCreditUnit,
            maxExtraCreditUnit: maxExtraCreditUnit,
            minCreditUnit: minCreditUnit,
            programmeId: programmeId,
            studentAcademicLevelId: studentAcademicLevelId,
            semesterSettingId: semesterSettingId,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int maxCreditUnit,
            required int maxExtraCreditUnit,
            required int minCreditUnit,
            required int programmeId,
            required int studentAcademicLevelId,
            Value<int?> semesterSettingId = const Value.absent(),
          }) =>
              CreditLoadLimitsTableCompanion.insert(
            id: id,
            maxCreditUnit: maxCreditUnit,
            maxExtraCreditUnit: maxExtraCreditUnit,
            minCreditUnit: minCreditUnit,
            programmeId: programmeId,
            studentAcademicLevelId: studentAcademicLevelId,
            semesterSettingId: semesterSettingId,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CreditLoadLimitsTableTableProcessedTableManager
    = ProcessedTableManager<
        _$LocalDatabase,
        $CreditLoadLimitsTableTable,
        CreditLoadLimitsTableData,
        $$CreditLoadLimitsTableTableFilterComposer,
        $$CreditLoadLimitsTableTableOrderingComposer,
        $$CreditLoadLimitsTableTableAnnotationComposer,
        $$CreditLoadLimitsTableTableCreateCompanionBuilder,
        $$CreditLoadLimitsTableTableUpdateCompanionBuilder,
        (
          CreditLoadLimitsTableData,
          BaseReferences<_$LocalDatabase, $CreditLoadLimitsTableTable,
              CreditLoadLimitsTableData>
        ),
        CreditLoadLimitsTableData,
        PrefetchHooks Function()>;

class $LocalDatabaseManager {
  final _$LocalDatabase _db;
  $LocalDatabaseManager(this._db);
  $$AnnouncementsTableTableTableManager get announcementsTable =>
      $$AnnouncementsTableTableTableManager(_db, _db.announcementsTable);
  $$SessionsTableTableTableManager get sessionsTable =>
      $$SessionsTableTableTableManager(_db, _db.sessionsTable);
  $$SemestersTableTableTableManager get semestersTable =>
      $$SemestersTableTableTableManager(_db, _db.semestersTable);
  $$CoursesTableTableTableManager get coursesTable =>
      $$CoursesTableTableTableManager(_db, _db.coursesTable);
  $$CourseRegistrationsTableTableTableManager get courseRegistrationsTable =>
      $$CourseRegistrationsTableTableTableManager(
          _db, _db.courseRegistrationsTable);
  $$CourseSpecificationsTableTableTableManager get courseSpecificationsTable =>
      $$CourseSpecificationsTableTableTableManager(
          _db, _db.courseSpecificationsTable);
  $$CreditLoadLimitsTableTableTableManager get creditLoadLimitsTable =>
      $$CreditLoadLimitsTableTableTableManager(_db, _db.creditLoadLimitsTable);
}

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localDatabaseHash() => r'dc7abbfdb67b853dbc578ba5d7ddbf4b0266197a';

/// See also [localDatabase].
@ProviderFor(localDatabase)
final localDatabaseProvider = Provider<LocalDatabase>.internal(
  localDatabase,
  name: r'localDatabaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localDatabaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalDatabaseRef = ProviderRef<LocalDatabase>;
// ignore: duplicate_ignore
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
