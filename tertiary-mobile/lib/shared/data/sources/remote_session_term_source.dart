import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/core/constants/endpoints.dart';
import 'package:tertiary_mobile/core/network/providers/api_client.dart';
import '../models/session_semester_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'remote_session_term_source.g.dart';

@riverpod
RemoteSessionTermSource remoteSessionTermSource(Ref ref) {
  return RemoteSessionTermSource(ref);
}

class RemoteSessionTermSource {
  final Ref ref;
  RemoteSessionTermSource(this.ref);

  Future<List<Session>> fetchSessions() async {
    final apiService = ref.read(apiClientProvider);
    final response = await apiService.get(
      ApiEndpoints.sessions,
      queryParameters: {'include_semesters': 'true'},
    );
    final data = response.data['sessions'] as List;
    final sessions = data.map((e) => Session.fromJson(e)).toList();
    return sessions;
  }
}
