import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';
import 'package:tertiary_mobile/shared/data/repositories/session_term_repository.dart';

part 'session_semester_provider.g.dart';

@riverpod
class SessionSemesterNotifier extends _$SessionSemesterNotifier {
  @override
  Future<List<Session>> build() async {
    // Keep provider alive while app is running
    ref.keepAlive();

    // Get cached data first for immediate display
    final repository = ref.read(sessionTermRepositoryProvider);
    final cachedSessions = await repository.getCachedSessions();

    // If we have cached data, show it immediately
    if (cachedSessions.isNotEmpty) {
      // Start background refresh without blocking the UI
      _refreshInBackground();
      return cachedSessions;
    }

    // If no cached data, fall back to traditional fetch
    return await repository.fetchSessions();
  }

  Future<void> _refreshInBackground() async {
    final repository = ref.read(sessionTermRepositoryProvider);

    try {
      // Fetch fresh data in background
      final freshSessions = await repository.fetchSessions();

      // Update state with fresh data when available
      state = AsyncValue.data(freshSessions);
    } catch (error) {
      // Silently fail background refresh - user still sees cached data
      // Could add logging here if needed
    }
  }

  Future<void> refresh() async {
    // For manual refresh, show loading state and fetch fresh data
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(sessionTermRepositoryProvider);
      return await repository.fetchSessions();
    });
  }
}

@riverpod
class SelectedSessionTermNotifier extends _$SelectedSessionTermNotifier {
  @override
  SelectedSessionTerm build() => const SelectedSessionTerm();

  void setSession(int sessionId, List<Session> sessions) {
    final session = sessions.firstWhere((s) => s.id == sessionId,
        orElse: () => sessions.first);
    final firstTermId =
        session.semesters.isNotEmpty ? session.semesters.first.id : null;
    state = state.copyWith(
      sessionId: sessionId,
      termId: firstTermId,
    );
  }

  void setTerm(int termId) {
    state = state.copyWith(termId: termId);
  }
}
