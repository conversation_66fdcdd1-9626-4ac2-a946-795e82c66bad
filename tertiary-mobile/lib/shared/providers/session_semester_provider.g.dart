// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_semester_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionSemesterNotifierHash() =>
    r'0db20cdd8ec2933adfe0f1b63e0e4681ba627640';

/// See also [SessionSemesterNotifier].
@ProviderFor(SessionSemesterNotifier)
final sessionSemesterNotifierProvider = AutoDisposeAsyncNotifierProvider<
    SessionSemesterNotifier, List<Session>>.internal(
  SessionSemesterNotifier.new,
  name: r'sessionSemesterNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sessionSemesterNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SessionSemesterNotifier = AutoDisposeAsyncNotifier<List<Session>>;
String _$selectedSessionTermNotifierHash() =>
    r'c8e7dbedfc78025fadbf2304f90362e745b3ef4c';

/// See also [SelectedSessionTermNotifier].
@ProviderFor(SelectedSessionTermNotifier)
final selectedSessionTermNotifierProvider = AutoDisposeNotifierProvider<
    SelectedSessionTermNotifier, SelectedSessionTerm>.internal(
  SelectedSessionTermNotifier.new,
  name: r'selectedSessionTermNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedSessionTermNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedSessionTermNotifier
    = AutoDisposeNotifier<SelectedSessionTerm>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
