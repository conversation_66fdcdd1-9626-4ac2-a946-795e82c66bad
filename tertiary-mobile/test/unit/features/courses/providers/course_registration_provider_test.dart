import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_response.dart';
import 'package:tertiary_mobile/features/courses/data/models/credit_load_limit.dart';
import 'package:tertiary_mobile/features/courses/data/repositories/courses_repository.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/features/courses/providers/course_registration_provider.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

class MockCoursesRepository extends Mock implements CoursesRepository {}

void main() {
  late ProviderContainer container;
  late MockCoursesRepository mockRepo;

  final course = Course(
    id: 1,
    courseCode: 'CSC101',
    courseTitle: 'Introduction to Computer Science',
    creditUnit: 3,
    courseSynopsis: 'Basic computer science concepts',
    type: 'Core',
    passMark: 40,
    lecturers: ['Dr<PERSON> <PERSON>', 'Prof<PERSON>'],
  );

  final session = Session(
    id: 1,
    sessionName: '2023/2024',
    semesters: [
      Semester(
        id: 1,
        title: 'First Semester',
        accronym: 'FIRST',
        position: 1,
        sessionId: 1,
      ),
    ],
  );

  final courseSpecification = CourseSpecification(
    id: 1,
    creditUnit: 3,
    units: '3',
    courseStatus: 'Active',
    courseStatusId: '1',
    semesterAccronym: 'FIRST',
    semesterTitle: 'First Semester',
    status: 1,
    passMarkRequired: 40,
    isSiwes: 0,
    hasPreRequisites: 0,
    isUsedBeyondQualifierLevel: 1,
    isUsedInResultComputation: 1,
    session: session,
    course: course,
  );

  setUp(() {
    mockRepo = MockCoursesRepository();
    container = ProviderContainer(
      overrides: [
        coursesRepositoryProvider.overrideWithValue(mockRepo),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  test('build returns empty state when no session ID is provided', () async {
    final result =
        await container.read(courseRegistrationNotifierProvider(null).future);
    expect(result.selected, isEmpty);
    expect(result.specifications, isEmpty);
    verifyNever(() => mockRepo.fetchCourseSpecifications(any()));
  });

  test('build fetches course specifications when session ID is provided',
      () async {
    when(() => mockRepo.fetchCourseSpecifications(1)).thenAnswer(
      (_) async => CourseSpecificationResponse(
        sessionId: 1,
        sessionName: '2023/2024',
        courseSpecifications: [courseSpecification],
        creditLoadLimits: [],
      ),
    );

    final result =
        await container.read(courseRegistrationNotifierProvider(1).future);

    expect(result.specifications, hasLength(1));
    expect(result.specifications.first.courseCode, 'CSC101');
    expect(result.creditLoadLimits, isEmpty);
    verify(() => mockRepo.fetchCourseSpecifications(1)).called(1);
  });

  test('refresh updates state with loading and new data', () async {
    final response = CourseSpecificationResponse(
      sessionId: 1,
      sessionName: '2023/2024',
      courseSpecifications: [courseSpecification],
      creditLoadLimits: [
        const CreditLoadLimit(
          id: 1,
          maxCreditUnit: 24,
          maxExtraCreditUnit: 30,
          minCreditUnit: 15,
          programmeId: 1,
          studentAcademicLevelId: 1,
        ),
      ],
    );
    when(() => mockRepo.fetchCourseSpecifications(1))
        .thenAnswer((_) async => response);

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);
    final future = notifier.refresh(1);

    // Verify loading state
    expect(
      container.read(courseRegistrationNotifierProvider(1)),
      isA<AsyncLoading<CourseRegistrationState>>(),
    );

    // Wait for refresh to complete
    await future;

    // Verify final state
    final state = container.read(courseRegistrationNotifierProvider(1));
    expect(state, isA<AsyncData<CourseRegistrationState>>());
    expect(state.value?.specifications, hasLength(1));
    expect(state.value?.creditLoadLimits, hasLength(1));
    verify(() => mockRepo.fetchCourseSpecifications(1)).called(1);
  });

  test('addCourse adds course to selected list and marks as registered',
      () async {
    when(() => mockRepo.fetchCourseSpecifications(1)).thenAnswer(
      (_) async => CourseSpecificationResponse(
        sessionId: 1,
        sessionName: '2023/2024',
        courseSpecifications: [courseSpecification],
        creditLoadLimits: [],
      ),
    );

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);
    await container.read(courseRegistrationNotifierProvider(1).future);

    final tableItem = courseSpecification.toTableItem();
    notifier.addCourse(tableItem);

    final state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected, hasLength(1));
    expect(state.selected.first.courseCode, 'CSC101');
    expect(state.selected.first.isRegistered, isTrue);
    expect(state.specifications.first.isRegistered, isTrue);
  });

  test(
      'removeCourse removes course from selected list and marks as unregistered',
      () async {
    when(() => mockRepo.fetchCourseSpecifications(1)).thenAnswer(
      (_) async => CourseSpecificationResponse(
        sessionId: 1,
        sessionName: '2023/2024',
        courseSpecifications: [courseSpecification],
        creditLoadLimits: [],
      ),
    );

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);
    await container.read(courseRegistrationNotifierProvider(1).future);

    // Add then remove course
    final tableItem = courseSpecification.toTableItem();
    notifier.addCourse(tableItem);
    notifier.removeCourse(tableItem);

    final state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected, isEmpty);
    expect(state.specifications.first.isRegistered, isFalse);
  });

  test('syncSelectedCourses updates selected courses list', () async {
    when(() => mockRepo.fetchCourseSpecifications(1)).thenAnswer(
      (_) async => CourseSpecificationResponse(
        sessionId: 1,
        sessionName: '2023/2024',
        courseSpecifications: [courseSpecification],
        creditLoadLimits: [],
      ),
    );

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);
    await container.read(courseRegistrationNotifierProvider(1).future);

    final tableItem =
        courseSpecification.toTableItem().copyWith(isRegistered: true);
    notifier.syncSelectedCourses([tableItem]);

    final state = container.read(courseRegistrationNotifierProvider(1)).value!;
    expect(state.selected, hasLength(1));
    expect(state.selected.first.courseCode, 'CSC101');
  });

  test('submitRegistration throws when no courses selected', () async {
    when(() => mockRepo.fetchCourseSpecifications(1)).thenAnswer(
      (_) async => CourseSpecificationResponse(
        sessionId: 1,
        sessionName: '2023/2024',
        courseSpecifications: [courseSpecification],
        creditLoadLimits: [],
      ),
    );

    final notifier =
        container.read(courseRegistrationNotifierProvider(1).notifier);
    await container.read(courseRegistrationNotifierProvider(1).future);

    expect(
      () => notifier.submitRegistration(),
      throwsA(isA<Exception>().having(
        (e) => e.toString(),
        'error message',
        contains('No courses selected for registration'),
      )),
    );
  });
}
