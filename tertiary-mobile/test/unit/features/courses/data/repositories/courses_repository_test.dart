import 'package:flutter_test/flutter_test.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_response.dart';
import 'package:tertiary_mobile/features/courses/data/models/credit_load_limit.dart';
import 'package:tertiary_mobile/features/courses/data/repositories/courses_repository.dart';
import 'package:tertiary_mobile/features/courses/data/sources/local_course_specifications_source.dart';
import 'package:tertiary_mobile/features/courses/data/sources/local_courses_source.dart';
import 'package:tertiary_mobile/features/courses/data/sources/local_credit_load_limits_source.dart';
import 'package:tertiary_mobile/features/courses/data/sources/remote_courses_source.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

class MockRemoteCoursesSource extends Mock implements RemoteCoursesSource {}

class MockLocalCourseRegistrationsSource extends Mock
    implements LocalCourseRegistrationsSource {}

class MockLocalCourseSpecificationsSource extends Mock
    implements LocalCourseSpecificationsSource {}

class MockLocalCreditLoadLimitsSource extends Mock
    implements LocalCreditLoadLimitsSource {}

class MockInternetConnectionChecker extends Mock
    implements InternetConnectionChecker {}

void main() {
  late MockRemoteCoursesSource remote;
  late MockLocalCourseRegistrationsSource localRegistrations;
  late MockLocalCourseSpecificationsSource localSpecs;
  late MockLocalCreditLoadLimitsSource localCreditLimits;
  late MockInternetConnectionChecker checker;
  late CoursesRepository repository;

  final course = Course(
    id: 1,
    courseCode: 'CSC101',
    courseTitle: 'Introduction to Computer Science',
    creditUnit: 3,
    courseSynopsis: 'Basic computer science concepts',
    type: 'Core',
    passMark: 40,
    lecturers: ['Dr. Smith', 'Prof. Johnson'],
  );

  final session = Session(
    id: 1,
    sessionName: '2023/2024',
    semesters: [
      Semester(
        id: 1,
        title: 'First Semester',
        accronym: 'FIRST',
        position: 1,
        sessionId: 1,
      ),
    ],
  );

  final courseRegistration = CourseRegistration(
    id: 1,
    course: course,
    session: session,
    semesterAccronym: 'FIRST',
    semesterPosition: 1,
    semesterTitle: 'First Semester',
    score: '85',
    courseStatus: 'Registered',
    approvalStatus: 'Approved',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  );

  final courseSpecification = CourseSpecification(
    id: 1,
    creditUnit: 3,
    units: '3',
    courseStatus: 'Active',
    courseStatusId: '1',
    semesterAccronym: 'FIRST',
    semesterTitle: 'First Semester',
    status: 1,
    passMarkRequired: 40,
    isSiwes: 0,
    hasPreRequisites: 0,
    isUsedBeyondQualifierLevel: 1,
    isUsedInResultComputation: 1,
    session: session,
    course: course,
  );

  setUp(() {
    remote = MockRemoteCoursesSource();
    localRegistrations = MockLocalCourseRegistrationsSource();
    localSpecs = MockLocalCourseSpecificationsSource();
    localCreditLimits = MockLocalCreditLoadLimitsSource();
    checker = MockInternetConnectionChecker();
    repository = CoursesRepository(
        remote, localRegistrations, localSpecs, localCreditLimits, checker);
  });

  group('Course Registrations', () {
    test(
        'fetchCourseRegistrations fetches from remote and caches locally if online',
        () async {
      when(() => checker.hasConnection).thenAnswer((_) async => true);
      when(() => remote.fetchCourseRegistrations(1))
          .thenAnswer((_) async => [courseRegistration]);
      when(() => localRegistrations.saveCourseRegistrations(any()))
          .thenAnswer((_) async {});

      final result = await repository.fetchCourseRegistrations(1);
      expect(result, isA<List<CourseRegistration>>());
      expect(result.first.course.courseCode, 'CSC101');
      verify(() => remote.fetchCourseRegistrations(1)).called(1);
      verify(() =>
              localRegistrations.saveCourseRegistrations([courseRegistration]))
          .called(1);
    });

    test('fetchCourseRegistrations returns cached data if offline', () async {
      when(() => checker.hasConnection).thenAnswer((_) async => false);
      when(() => localRegistrations.getCourseRegistrations(1))
          .thenAnswer((_) async => [courseRegistration]);

      final result = await repository.fetchCourseRegistrations(1);
      expect(result, isA<List<CourseRegistration>>());
      expect(result.first.course.courseCode, 'CSC101');
      verifyNever(() => remote.fetchCourseRegistrations(1));
      verify(() => localRegistrations.getCourseRegistrations(1)).called(1);
    });

    test(
        'fetchCourseRegistrationsStaleWhileRevalidate yields cached data first when available',
        () async {
      when(() => localRegistrations.getCourseRegistrations(1))
          .thenAnswer((_) async => [courseRegistration]);
      when(() => checker.hasConnection).thenAnswer((_) async => true);
      when(() => remote.fetchCourseRegistrations(1))
          .thenAnswer((_) async => [courseRegistration.copyWith(score: '90')]);
      when(() => localRegistrations.saveCourseRegistrations(any()))
          .thenAnswer((_) async {});

      final stream = repository.fetchCourseRegistrations(1);
      final result = await stream;

      expect(result.length, 2);
      expect(result[0].score, '85'); // Cached data first
      expect(result[1].score, '90'); // Fresh data second
      verify(() => localRegistrations.getCourseRegistrations(1)).called(1);
      verify(() => remote.fetchCourseRegistrations(1)).called(1);
    });
  });

  group('Course Specifications', () {
    test(
        'fetchCourseSpecifications fetches from remote and caches locally if online',
        () async {
      final response = CourseSpecificationResponse(
        sessionId: 1,
        sessionName: '2023/2024',
        courseSpecifications: [courseSpecification],
        creditLoadLimits: [],
      );

      when(() => checker.hasConnection).thenAnswer((_) async => true);
      when(() => remote.fetchCourseSpecifications(1))
          .thenAnswer((_) async => response);
      when(() => localSpecs.saveCourseSpecifications(any()))
          .thenAnswer((_) async {});
      when(() => localCreditLimits.saveCreditLoadLimits(any()))
          .thenAnswer((_) async {});

      final result = await repository.fetchCourseSpecifications(1);
      expect(result, isA<CourseSpecificationResponse>());
      expect(result.courseSpecifications.first.course.courseCode, 'CSC101');
      verify(() => remote.fetchCourseSpecifications(1)).called(1);
      verify(() => localSpecs.saveCourseSpecifications(any())).called(1);
      verify(() => localCreditLimits.saveCreditLoadLimits(any())).called(1);
    });

    test('returns cached data when offline', () async {
      when(() => checker.hasConnection).thenAnswer((_) async => false);
      when(() => localSpecs.getCourseSpecifications(1))
          .thenAnswer((_) async => [courseSpecification]);
      when(() => localCreditLimits.getCreditLoadLimits())
          .thenAnswer((_) async => []);

      final result = await repository.fetchCourseSpecifications(1);
      expect(result, isA<CourseSpecificationResponse>());
      expect(result.courseSpecifications.first.course.courseCode, 'CSC101');
      verifyNever(() => remote.fetchCourseSpecifications(1));
      verify(() => localSpecs.getCourseSpecifications(1)).called(1);
      verify(() => localCreditLimits.getCreditLoadLimits()).called(1);
    });

    test('falls back to cached data when remote fetch fails', () async {
      final cachedSpecs = [courseSpecification];
      final cachedLimits = [
        const CreditLoadLimit(
          id: 1,
          maxCreditUnit: 24,
          maxExtraCreditUnit: 30,
          minCreditUnit: 15,
          programmeId: 1,
          studentAcademicLevelId: 1,
        ),
      ];

      when(() => checker.hasConnection).thenAnswer((_) async => true);
      when(() => remote.fetchCourseSpecifications(1))
          .thenThrow(Exception('Network error'));
      when(() => localSpecs.getCourseSpecifications(1))
          .thenAnswer((_) async => cachedSpecs);
      when(() => localCreditLimits.getCreditLoadLimits())
          .thenAnswer((_) async => cachedLimits);

      final result = await repository.fetchCourseSpecifications(1);
      expect(result, isA<CourseSpecificationResponse>());
      expect(result.courseSpecifications, equals(cachedSpecs));
      expect(result.creditLoadLimits, equals(cachedLimits));
      verify(() => remote.fetchCourseSpecifications(1)).called(1);
      verify(() => localSpecs.getCourseSpecifications(1)).called(1);
      verify(() => localCreditLimits.getCreditLoadLimits()).called(1);
    });
  });

  group('Course Registration Submission', () {
    test('submitCourseRegistrations submits when online', () async {
      when(() => checker.hasConnection).thenAnswer((_) async => true);
      when(() => remote.submitCourseRegistrations(any()))
          .thenAnswer((_) async {});

      final registrations = [
        {'course_id': 1, 'semester_id': 1, 'session_id': 1}
      ];

      await repository.submitCourseRegistrations(registrations);
      verify(() => remote.submitCourseRegistrations(registrations)).called(1);
    });

    test('submitCourseRegistrations throws exception when offline', () async {
      when(() => checker.hasConnection).thenAnswer((_) async => false);

      final registrations = [
        {'course_id': 1, 'semester_id': 1, 'session_id': 1}
      ];

      expect(
        () => repository.submitCourseRegistrations(registrations),
        throwsA(isA<Exception>()),
      );
      verifyNever(() => remote.submitCourseRegistrations(any()));
    });
  });
}
