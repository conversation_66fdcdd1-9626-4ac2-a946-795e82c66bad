import 'package:flutter_test/flutter_test.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tertiary_mobile/features/announcement/data/models/announcement_model.dart';
import 'package:tertiary_mobile/features/announcement/data/repositories/announcements_repository.dart';
import 'package:tertiary_mobile/features/announcement/data/sources/local_announcements_source.dart';
import 'package:tertiary_mobile/features/announcement/data/sources/remote_announcements_source.dart';

class MockRemoteAnnouncementsSource extends Mock
    implements RemoteAnnouncementsSource {}

class MockLocalAnnouncementsSource extends Mock
    implements LocalAnnouncementsSource {}

class MockInternetConnectionChecker extends Mock
    implements InternetConnectionChecker {}

void main() {
  late MockRemoteAnnouncementsSource remote;
  late MockLocalAnnouncementsSource local;
  late MockInternetConnectionChecker checker;
  late AnnouncementsRepository repository;

  final announcement = Announcement(
    id: 1,
    title: 'Test',
    message: 'Message',
    priority: 'high',
    announcer: 'Admin',
    recipient: 'All',
    level: '100',
    programmeLevel: 'Science',
    createdAt: DateTime(2023, 1, 1),
    updatedAt: DateTime(2023, 1, 1),
  );

  setUp(() {
    remote = MockRemoteAnnouncementsSource();
    local = MockLocalAnnouncementsSource();
    checker = MockInternetConnectionChecker();
    repository = AnnouncementsRepository(remote, local, checker);
  });

  test('fetchAnnouncements fetches from remote and caches locally if online',
      () async {
    when(() => checker.hasConnection).thenAnswer((_) async => true);
    when(() => remote.fetchAnnouncements())
        .thenAnswer((_) async => [announcement]);
    when(() => local.saveAnnouncements(any())).thenAnswer((_) async {});
    when(() => local.getAnnouncements())
        .thenAnswer((_) async => [announcement]);

    final result = await repository.fetchAnnouncements();
    expect(result, isA<List<Announcement>>());
    expect(result.first.title, 'Test');
    verify(() => remote.fetchAnnouncements()).called(1);
    verify(() => local.saveAnnouncements([announcement])).called(1);
  });

  test('fetchAnnouncements falls back to local if remote fails', () async {
    when(() => checker.hasConnection).thenAnswer((_) async => true);
    when(() => remote.fetchAnnouncements())
        .thenThrow(Exception('Remote error'));
    when(() => local.getAnnouncements())
        .thenAnswer((_) async => [announcement]);

    final result = await repository.fetchAnnouncements();
    expect(result, isA<List<Announcement>>());
    expect(result.first.title, 'Test');
    verify(() => remote.fetchAnnouncements()).called(1);
    verify(() => local.getAnnouncements()).called(1);
  });

  test('fetchAnnouncements fetches from local if offline', () async {
    when(() => checker.hasConnection).thenAnswer((_) async => false);
    when(() => local.getAnnouncements())
        .thenAnswer((_) async => [announcement]);

    final result = await repository.fetchAnnouncements();

    expect(result, isA<List<Announcement>>());
    expect(result.first.title, 'Test');
    verifyNever(() => remote.fetchAnnouncements());
    verify(() => local.getAnnouncements()).called(1);
  });

  test('fetchAnnouncements falls back to local when remote fails', () async {
    when(() => checker.hasConnection).thenAnswer((_) async => true);
    when(() => remote.fetchAnnouncements())
        .thenThrow(Exception('Network error'));
    when(() => local.getAnnouncements())
        .thenAnswer((_) async => [announcement]);

    final result = await repository.fetchAnnouncements();

    expect(result, isA<List<Announcement>>());
    expect(result.first.title, 'Test');
    verify(() => remote.fetchAnnouncements()).called(1);
    verify(() => local.getAnnouncements()).called(1);
  });

  // All tests now focus on the fetchAnnouncements method
}
