import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tertiary_mobile/features/announcement/data/models/announcement_model.dart';
import 'package:tertiary_mobile/features/announcement/data/repositories/announcements_repository.dart';
import 'package:tertiary_mobile/features/announcement/providers/announcements_provider.dart';

class MockAnnouncementsRepository extends Mock
    implements AnnouncementsRepository {}

void main() {
  late ProviderContainer container;
  late MockAnnouncementsRepository mockRepo;
  final announcement = Announcement(
    id: 1,
    title: 'Test',
    message: 'Message',
    priority: 'high',
    announcer: 'Admin',
    recipient: 'All',
    level: '100',
    programmeLevel: 'Science',
    createdAt: DateTime(2023, 1, 1),
    updatedAt: DateTime(2023, 1, 1),
  );

  setUp(() {
    mockRepo = MockAnnouncementsRepository();
    container = ProviderContainer(
      overrides: [
        announcementsRepositoryProvider.overrideWithValue(mockRepo),
      ],
    );
  });

  test('build fetches announcements from repository', () async {
    when(() => mockRepo.fetchAnnouncements())
        .thenAnswer((_) async => [announcement]);

    final result = await container.read(announcementsProvider.future);

    expect(result, isA<List<Announcement>>());
    expect(result.first.title, 'Test');
    verify(() => mockRepo.fetchAnnouncements()).called(1);
  });

  test('refresh sets loading and then updates state', () async {
    when(() => mockRepo.fetchAnnouncements())
        .thenAnswer((_) async => [announcement]);

    final notifier = container.read(announcementsProvider.notifier);
    final future = notifier.refresh('session');

    // Verify loading state
    expect(
      container.read(announcementsProvider),
      isA<AsyncLoading<List<Announcement>>>(),
    );

    // Wait for refresh to complete
    await future;

    // Verify final state
    final state = container.read(announcementsProvider);
    expect(state, isA<AsyncData<List<Announcement>>>());
    expect(state.value?.first.title, 'Test');
    verify(() => mockRepo.fetchAnnouncements()).called(1);
  });

  test('handles error when fetch fails', () async {
    when(() => mockRepo.fetchAnnouncements())
        .thenThrow(Exception('Failed to fetch'));

    final future = container.read(announcementsProvider.future);

    await expectLater(
      future,
      throwsA(isA<Exception>()),
    );

    verify(() => mockRepo.fetchAnnouncements()).called(1);
  });
}
